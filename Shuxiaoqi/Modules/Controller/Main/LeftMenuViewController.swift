//
//  LeftMenuViewController.swift
//  Shuxia<PERSON>qi
//
//  Created by yong<PERSON>ng ye on 2025/3/19.
//

import UIKit
import SnapKit
import GYSide
// 新增：导入 AuthCoordinator 用于统一登录流程

//首页左侧菜单
class LeftMenuViewController: UIViewController {
    
    // 容器视图 - 用于设置圆角
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .init(hex: "F5F5F5")
        // 设置右上角和右下角的圆角
        view.layer.cornerRadius = 50
        view.layer.maskedCorners = [.layerMaxXMinYCorner, .layerMaxXMaxYCorner]
        view.clipsToBounds = true
        return view
    }()
    
    // 头部区域视图
    private let headerView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    // 头部背景图视图
    private let headerBackgroundImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        return imageView
    }()
    
    // 头部背景蒙版 - 使用渐变色
    private let headerBackgroundOverlay: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        
        // 创建渐变层
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            UIColor.white.withAlphaComponent(0.2).cgColor, // 上方为0.2透明度的白色
            UIColor(hex: "#F5F5F5").cgColor                // 下方为F5F5F5色
        ]
        gradientLayer.locations = [0.0, 1.0]
        gradientLayer.startPoint = CGPoint(x: 0.5, y: 0.0)
        gradientLayer.endPoint = CGPoint(x: 0.5, y: 1.0)
        
        // 禁用隐式动画
        let animation = CATransaction()
        CATransaction.begin()
        CATransaction.setDisableActions(true)
        
        // 将渐变层添加到视图的图层中
        view.layer.addSublayer(gradientLayer)
        
        CATransaction.commit()
        
        return view
    }()
    
    // 头像容器 - 用于添加渐变边框
    private let avatarContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        view.layer.cornerRadius = 32 // 64/2
        view.clipsToBounds = false
        return view
    }()
    
    // 渐变边框层
    private let gradientBorderLayer: CAGradientLayer = {
        let layer = CAGradientLayer()
        layer.colors = [
            UIColor(hex: "#FF5900").cgColor,
            UIColor(hex: "#FF8D36").cgColor
        ]
        layer.startPoint = CGPoint(x: 0, y: 0)
        layer.endPoint = CGPoint(x: 1, y: 1)
        layer.cornerRadius = 32 // 64/2
        return layer
    }()
    
    // 头像遮罩 - 白色背景，用于在渐变边框和头像之间创建间隙
    private let avatarMask: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 30 // (64-4)/2
        view.clipsToBounds = true
        return view
    }()
    
    // 头像图片视图
    private let avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.backgroundColor = UIColor(hex: "#444444") // 占位背景色
        imageView.layer.cornerRadius = 30 // (64-4)/2
        imageView.clipsToBounds = true
        imageView.contentMode = .scaleAspectFill
        // 可以设置默认头像
        imageView.image = UIImage(named: "default_avatar")
        return imageView
    }()
    
    // 添加用户名标签
    private let usernameLabel: UILabel = {
        let label = UILabel()
        label.text = "点击登录"
        label.textColor = UIColor(hex: "#333333")
        label.font = .boldSystemFont(ofSize: 18)
        return label
    }()
    
    // 添加粉丝数标签
    private let followersLabel: UILabel = {
        let label = UILabel()
        label.text = "粉丝: 0"
        label.textColor = UIColor(hex: "#777777")
        label.font = .systemFont(ofSize: 12)
        return label
    }()
    
    // 添加收藏夹按钮
    private let favoritesButton: UIButton = {
        // 新代码：使用 UIButton.Configuration
        var configuration = UIButton.Configuration.plain()
        configuration.title = "收藏夹 0"
        configuration.image = UIImage(named: "left_menu_right_arrow")
        configuration.imagePlacement = .trailing // 图像放在文本后面
        configuration.imagePadding = 4 // 设置图像与文本的间距为4pt
        
        // 设置标题颜色和字体
        configuration.baseForegroundColor = UIColor(hex: "#333333")
        
        // 创建按钮
        let button = UIButton(configuration: configuration)
        
        // 设置标题字体需要使用 UIConfigurationTextAttributesTransformer
        button.configurationUpdateHandler = { button in
            button.configuration?.titleTextAttributesTransformer = UIConfigurationTextAttributesTransformer { attributes in
                var newAttributes = attributes
                newAttributes.font = .systemFont(ofSize: 14, weight: .bold)
                return newAttributes
            }
        }
        
        return button
    }()
    
    // 添加收藏封面列表
    private let favoritesCollectionView: UICollectionView = {
        // 创建水平滚动的布局
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.itemSize = CGSize(width: 72, height: 128)
        layout.minimumLineSpacing = 12 // 设置项目之间的间距
        
        // 创建集合视图
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = UIColor(hex: "#F5F5F5")
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.contentInset = UIEdgeInsets(top: 0, left: 28, bottom: 0, right: 20)
        
        // 注册单元格
        collectionView.register(FavoriteCell.self, forCellWithReuseIdentifier: "FavoriteCell")
        
        return collectionView
    }()
    
    // 修改渐变蒙版的初始化
    private var gradientMaskView: UIView!
    
    // 菜单按钮数据
    private let menuItems: [(icon: String, title: String)] = [
        ("search_icon", "搜索"),
        ("scan_icon", "扫一扫"),
        // 将“点赞记录”替换为“评论历史”（图标暂不改）
        ("ic_review_history", "评论历史"),
        ("shop_icon", "商城"),
        ("coupon_icon", "优惠券"),
        ("settings_icon", "设置")
    ]
    
    // 菜单按钮容器
    private let menuStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 0 // 按钮之间默认没有间距
        stackView.alignment = .leading
        stackView.distribution = .fill // 使用fill以便自定义高度
        return stackView
    }()
    
    // 标记是否已调整按钮高度
    private var didAdjustButtonHeight = false

    // 添加状态属性 - 移除 isLoggedIn
    // private var isLoggedIn: Bool = false // 默认为未登录
    private var favoriteItems: [VideoItem] = [] // 使用 VideoItem 类型
    // 新增：存储用户信息 (如果需要)
    // private var userProfile: UserProfile? // 假设你有一个 UserProfile 模型
    // 新增：标记是否有收藏项
    private var hasFavorites: Bool = false

    // 添加未登录提示视图
    private let loginPromptView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear // 透明背景
        view.isUserInteractionEnabled = true // 允许交互
        // 添加一个提示标签
        let label = UILabel()
        label.text = "点击登录"
        label.textColor = UIColor(hex: "#333333")
        label.font = .boldSystemFont(ofSize: 18)
        view.addSubview(label)
        label.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        return view
    }()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 禁用所有层的隐式动画
        CATransaction.begin()
        CATransaction.setDisableActions(true)
        
        setupUI()
        
        // 确保立即布局并渲染
        view.layoutIfNeeded()
        
        CATransaction.commit()

        // 设置收藏封面列表的代理和数据源
        favoritesCollectionView.delegate = self
        favoritesCollectionView.dataSource = self

        // 应用初始UI状态
        updateUIForState()
        // 如果已登录，获取用户信息
        if AuthManager.shared.isLoggedIn {
            // 重置收藏状态，等待 API 返回结果
            hasFavorites = false
            fetchUserData()
        }

        // 添加登录提示视图的点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(loginPromptTapped))
        loginPromptView.addGestureRecognizer(tapGesture)
    }
    
    private func setupUI() {
        view.backgroundColor = .clear
        
        // 添加容器视图
        view.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.width.equalTo(UIScreen.main.bounds.width * 0.7) // 宽度为屏幕宽度的70%
        }
        
        // 添加头部区域视图
        containerView.addSubview(headerView)
        headerView.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
            make.height.equalTo(containerView.snp.width) // 正方形头部区域
        }
        
        // 添加头部背景图视图
        headerView.addSubview(headerBackgroundImageView)
        headerBackgroundImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 添加头部背景蒙版
        headerView.addSubview(headerBackgroundOverlay)
        headerBackgroundOverlay.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 添加头部背景图视图
        headerView.addSubview(headerBackgroundImageView)
        headerBackgroundImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 添加头部背景蒙版（禁用隐式动画）
        CATransaction.begin()
        CATransaction.setDisableActions(true)
        
        headerView.addSubview(headerBackgroundOverlay)
        headerBackgroundOverlay.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        CATransaction.commit()
        
        // 添加头部背景图视图（禁用隐式动画）
        CATransaction.begin()
        CATransaction.setDisableActions(true)
        
        headerView.addSubview(headerBackgroundImageView)
        headerBackgroundImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 添加头部背景蒙版
        headerView.addSubview(headerBackgroundOverlay)
        headerBackgroundOverlay.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        CATransaction.commit()
        
        // 添加容器视图
        view.backgroundColor = .clear
        
        // 添加容器视图
        view.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.width.equalTo(UIScreen.main.bounds.width * 0.7) // 宽度为屏幕宽度的70%
        }
        
        // 添加头部区域视图
        containerView.addSubview(headerView)
        headerView.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
            make.height.equalTo(containerView.snp.width) // 正方形头部区域
        }

        // 禁用所有隐式动画
        CATransaction.begin()
        CATransaction.setDisableActions(true)
        
        // 添加头部背景图视图
        headerView.addSubview(headerBackgroundImageView)
        headerBackgroundImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 添加头部背景蒙版
        headerView.addSubview(headerBackgroundOverlay)
        headerBackgroundOverlay.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        CATransaction.commit()
        
        // 添加头像容器
        containerView.addSubview(avatarContainer)
        avatarContainer.snp.makeConstraints { make in
            make.left.equalTo(30)
            make.top.equalTo(94)
            make.size.equalTo(64)
        }

        // 允许头像容器交互并添加点击手势（未登录时触发登录）
        avatarContainer.isUserInteractionEnabled = true
        let avatarTap = UITapGestureRecognizer(target: self, action: #selector(avatarTapped))
        avatarContainer.addGestureRecognizer(avatarTap)
        
        // 设置渐变边框
        gradientBorderLayer.frame = CGRect(x: 0, y: 0, width: 64, height: 64)
        avatarContainer.layer.addSublayer(gradientBorderLayer)
        
        // 添加头像遮罩
        avatarContainer.addSubview(avatarMask)
        avatarMask.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.size.equalTo(60) // 64-4
        }
        
        // 添加头像
        avatarContainer.addSubview(avatarImageView)
        avatarImageView.snp.makeConstraints { make in
            make.edges.equalTo(avatarMask)
        }
        
        // 添加用户名标签
        containerView.addSubview(usernameLabel)
        usernameLabel.snp.makeConstraints { make in
            make.left.equalTo(avatarContainer)
            make.top.equalTo(avatarContainer.snp.bottom).offset(16)
        }
        
        // 添加粉丝数标签
        containerView.addSubview(followersLabel)
        followersLabel.snp.makeConstraints { make in
            make.left.equalTo(usernameLabel)
            make.top.equalTo(usernameLabel.snp.bottom).offset(8)
            make.height.equalTo(12)
        }

        // 为粉丝标签添加点击手势
        followersLabel.isUserInteractionEnabled = true
        let followersTap = UITapGestureRecognizer(target: self, action: #selector(followersLabelTapped))
        followersLabel.addGestureRecognizer(followersTap)
        
        // 添加登录提示视图 (覆盖头像、用户名、粉丝区域) -> 修改为对齐用户名位置
        containerView.addSubview(loginPromptView)
        loginPromptView.snp.remakeConstraints { make in // 使用 remake 以防万一之前有约束
            make.centerY.equalTo(usernameLabel)     // 垂直中心与用户名对齐
            make.centerX.equalTo(avatarImageView)
            make.height.equalTo(30)                 // 设置一个合适的点击高度
            make.right.lessThanOrEqualToSuperview().offset(-30) // 右侧不要超出边界
        }
        loginPromptView.isHidden = true // 初始隐藏

        // 确保登录提示视图在最前面，不会被头像等遮挡点击事件
        containerView.bringSubviewToFront(loginPromptView)

        // 添加收藏夹按钮
        containerView.addSubview(favoritesButton)
        favoritesButton.snp.makeConstraints { make in
            make.left.equalTo(25)
            make.top.equalTo(headerView.snp.bottom).offset(0) // 紧贴头部区域
            make.height.equalTo(14)
        }
        
        // 添加收藏封面列表
        containerView.addSubview(favoritesCollectionView)
        favoritesCollectionView.snp.makeConstraints { make in
            make.left.equalTo(6)
            make.right.equalToSuperview()
            make.top.equalTo(favoritesButton.snp.bottom).offset(12)
            make.height.equalTo(128)
        }
        
        // 创建并添加渐变蒙版 - 移到这里确保在收藏列表之后添加
        setupGradientMaskView()
        
        // 添加菜单按钮容器
        containerView.addSubview(menuStackView)
        menuStackView.snp.makeConstraints { make in
            make.left.equalTo(30)
            make.top.equalTo(favoritesCollectionView.snp.bottom).offset(20) // 距离收藏列表下方20pt
            make.right.equalToSuperview()
            
            // 添加底部约束，确保有足够的底部间距
            let bottomSafeAreaInset = WindowUtil.safeAreaBottom;            make.bottom.lessThanOrEqualTo(containerView).offset(-(bottomSafeAreaInset + 20))
        }
        
        // 创建菜单按钮
        setupMenuButtons()
        
        // 设置背景图
        updateHeaderBackgroundImage()
        
        // 添加收藏夹按钮点击事件
        favoritesButton.addTarget(self, action: #selector(favoritesButtonTapped), for: .touchUpInside)
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        
        // 禁用隐式动画
        CATransaction.begin()
        CATransaction.setDisableActions(true)
        
        // 更新渐变边框的大小
        gradientBorderLayer.frame = CGRect(x: 0, y: 0, width: 64, height: 64)
        
        // 更新头部背景渐变蒙版的大小
        if let gradientLayer = headerBackgroundOverlay.layer.sublayers?.first as? CAGradientLayer {
            gradientLayer.frame = headerBackgroundOverlay.bounds
        }
        
        // 更新渐变蒙版的大小
        if let gradientLayer = gradientMaskView.layer.sublayers?.first as? CAGradientLayer {
            gradientLayer.frame = gradientMaskView.bounds
        }
        
        CATransaction.commit()
        
        // 如果是第一次布局，调整按钮高度
        if !didAdjustButtonHeight && containerView.bounds.height > 0 {
            adjustButtonHeights()
            didAdjustButtonHeight = true
        }
    }
    
    // 更新头部背景图方法（仅用于初始化和未登录状态）
    private func updateHeaderBackgroundImage() {
        // 设置背景为默认颜色
        headerBackgroundImageView.image = nil
        headerBackgroundImageView.backgroundColor = UIColor(hex: "#F5F5F5")

        // 注意：已登录状态下的背景图设置应该在 updateUserInfoUI() 方法中进行
        // 这样可以确保使用正确的API数据，避免二次设置问题
    }
    
    // 收藏夹按钮点击事件
    @objc private func favoritesButtonTapped() {
        // 先检查登录状态
        guard AuthManager.shared.isLoggedIn else {
            print("需要登录才能查看收藏夹")
            loginPromptTapped() // 触发登录
            return
        }

        print("收藏夹按钮被点击")
        navigateToViewController(MyCollectionViewController())
        // 这里添加收藏夹按钮的处理逻辑
    }
    
    // 创建菜单按钮
    private func setupMenuButtons() {
        // 计算可用空间 (减去底部安全区域)
        let bottomSafeAreaInset = WindowUtil.safeAreaBottom
        let totalAvailableHeight = containerView.bounds.height -
                                  (headerView.bounds.height +
                                   favoritesCollectionView.bounds.height +
                                   20 + // 收藏列表下方间距
                                   bottomSafeAreaInset + 20) // 底部安全距离 + 额外底部间距
        
        // 计算所需的总高度 (所有按钮高度 + 设置按钮上方的额外间距)
        let defaultButtonHeight = 50.0
        let settingsMargin = 25.0
        let requiredHeight = (defaultButtonHeight * CGFloat(menuItems.count)) + settingsMargin
        
        // 如果空间不足，计算调整后的按钮高度
        let buttonHeight: CGFloat
        if totalAvailableHeight < requiredHeight {
            // 计算调整后的按钮高度 (最小30pt)
            buttonHeight = max(30, (totalAvailableHeight - settingsMargin) / CGFloat(menuItems.count))
        } else {
            buttonHeight = defaultButtonHeight
        }
        
        // 创建按钮
        for (index, item) in menuItems.enumerated() {
            let button = createMenuButton(icon: item.icon, title: item.title)
            button.tag = index // 添加这一行，设置按钮的tag为其索引
            
            // 如果是最后一个按钮（设置），添加额外的间距视图
            if index == menuItems.count - 1 {
                let spacerView = UIView()
                spacerView.backgroundColor = .clear
                menuStackView.addArrangedSubview(spacerView)
                
                // 设置间距视图高度
                spacerView.snp.makeConstraints { make in
                    make.width.equalTo(containerView.snp.width).offset(-60)
                    make.height.equalTo(25) // 设置按钮上方25pt间距
                }
            }
            
            menuStackView.addArrangedSubview(button)
            
            // 设置按钮的宽度和高度约束
            button.snp.makeConstraints { make in
                make.width.equalTo(containerView.snp.width).offset(-60) // 左右各留30pt的边距
                make.height.equalTo(buttonHeight) // 自适应高度
            }
        }
    }
    
    // 创建单个菜单按钮
    private func createMenuButton(icon: String, title: String) -> UIButton {
        // 新代码：使用 UIButton.Configuration
        var configuration = UIButton.Configuration.plain()
        
        // 准备图标
        var finalImage: UIImage?
        if let image = UIImage(named: icon) {
            // 确保图标大小为28x28
            finalImage = resizeImage(image, targetSize: CGSize(width: 28, height: 28))
        } else {
            // 如果图标不存在，创建一个占位图标
            let placeholderView = UIView(frame: CGRect(x: 0, y: 0, width: 28, height: 28))
            placeholderView.backgroundColor = .lightGray
            placeholderView.layer.cornerRadius = 4
            
            UIGraphicsBeginImageContextWithOptions(placeholderView.bounds.size, false, 0)
            if let context = UIGraphicsGetCurrentContext() {
                placeholderView.layer.render(in: context)
                finalImage = UIGraphicsGetImageFromCurrentImageContext()
                UIGraphicsEndImageContext()
            }
        }
        
        // 设置图标和标题
        configuration.image = finalImage
        configuration.title = title
        
        // 设置图标和文字之间的间距为16pt
        configuration.imagePadding = 16
        
        // 设置图标位置
        configuration.imagePlacement = .leading
        
        // 设置内容对齐
        configuration.contentInsets = NSDirectionalEdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 0)
        
        // 设置标题颜色
        configuration.baseForegroundColor = UIColor(hex: "#333333")
        
        // 创建按钮
        let button = UIButton(configuration: configuration)
        
        // 设置标题字体
        button.configurationUpdateHandler = { button in
            button.configuration?.titleTextAttributesTransformer = UIConfigurationTextAttributesTransformer { attributes in
                var newAttributes = attributes
                newAttributes.font = .systemFont(ofSize: 16)
                return newAttributes
            }
        }
        
        // 设置内容对齐方式为左对齐
        button.contentHorizontalAlignment = .leading
        
        // 添加点击事件
        button.addTarget(self, action: #selector(menuButtonTapped(_:)), for: .touchUpInside)
        
        return button
    }
    
    // 辅助方法：调整图像大小
    private func resizeImage(_ image: UIImage, targetSize: CGSize) -> UIImage {
        let size = image.size
        
        let widthRatio  = targetSize.width  / size.width
        let heightRatio = targetSize.height / size.height
        
        // 保持宽高比
        let ratio = min(widthRatio, heightRatio)
        let newSize = CGSize(width: size.width * ratio, height: size.height * ratio)
        
        let rect = CGRect(x: 0, y: 0, width: newSize.width, height: newSize.height)
        
        UIGraphicsBeginImageContextWithOptions(newSize, false, 0)
        image.draw(in: rect)
        let newImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        
        return newImage ?? image
    }
    
    // 菜单按钮点击事件
    @objc private func menuButtonTapped(_ sender: UIButton) {
        // 先检查登录状态
        guard AuthManager.shared.isLoggedIn else {
            print("需要登录才能使用菜单功能")
            loginPromptTapped() // 触发登录
            return
        }

        let index = sender.tag
        
        if index >= 0 && index < menuItems.count {
            let item = menuItems[index]
            print("点击了菜单按钮: \(item.title)")
            
            // 关闭侧边菜单
            dismissSideMenu()
            
            // 根据不同的菜单项执行不同的操作
            switch index {
            case 0: // 搜索
                // 处理搜索操作
                navigateToViewController(SearchViewController())
                break
            case 1: // 扫一扫
                // 处理扫一扫操作
                navigateToViewController(QRScanViewController())
                break
            case 2: // 评论历史
                // 处理评论历史操作
                navigateToViewController(CommentHistoryViewController())
                break
            case 3: // 商城
                // 处理商城操作
                navigateToViewController(WebViewController(path: "", title: "商城"))
                break
            case 4: // 优惠券
                // 处理优惠券操作
                //跳转H5 路径actPage/couponBox/couponBox
                navigateToViewController(WebViewController(path: "userPage/myCard/myCard", title: "我的券包"))
                break
            case 5: // 设置
                // 处理设置操作
                navigateToViewController(SettingViewController())
                break
            default:
                break
            }
        }
    }
    
    // 关闭侧边菜单
    private func dismissSideMenu() {
        // 使用通知中心发送关闭侧边菜单的通知
        NotificationCenter.default.post(name: NSNotification.Name("CloseSideMenuNotification"), object: nil)
    }
    
    // MARK: - 统一登录流程
    /// 触发统一登录流程（一键登录优先，失败后自动转普通登录页）
    private func startLoginProcess() {
        // 避免已登录或正在登录时重复调用
        guard !AuthManager.shared.isLoggedIn else { return }

        // 找到作为 presenter 的 CustomTabBarController
        guard let presenter = findCustomTabBarController() else {
            print("startLoginProcess: 未找到 CustomTabBarController，取消登录流程")
            return
        }

        // 关闭侧边菜单后再调用登录，以免出现两个模态视图重叠
        dismissSideMenu()
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            AuthCoordinator.shared.startLogin(from: presenter) { success in
                print("AuthCoordinator 登录流程结束：\(success ? "成功" : "失败/取消")")
            }
        }
    }

    // MARK: - 点击事件
    /// 登录提示视图点击
    @objc private func loginPromptTapped() {
        print("loginPromptTapped 被触发 → 调用登录流程")
        startLoginProcess()
    }

    /// 头像点击（未登录时触发登录，已登录时跳转资料编辑页）
    @objc private func avatarTapped() {
        if AuthManager.shared.isLoggedIn {
            // 已登录，跳转到资料编辑页
            print("avatarTapped 被触发 → 跳转到资料编辑页")
            let vc = UserInformationEditingPage()
            navigateToViewController(vc)
        } else {
            // 未登录，触发登录流程
            print("avatarTapped 被触发 → 调用登录流程")
            startLoginProcess()
        }
    }

    /// 粉丝标签点击（已登录时跳转粉丝列表）
    @objc private func followersLabelTapped() {
        // 先检查登录状态
        guard AuthManager.shared.isLoggedIn else {
            print("需要登录才能查看粉丝列表")
            startLoginProcess() // 触发登录
            return
        }

        print("followersLabelTapped 被触发 → 跳转到粉丝列表")
        let followListVC = FollowListViewController()
        followListVC.selectedSegmentIndex = 1 // 1表示粉丝页面
        navigateToViewController(followListVC)
    }
    
    // 导航到指定视图控制器
    private func navigateToViewController(_ viewController: UIViewController) {
        // 先关闭侧边菜单
        dismissSideMenu()
        
        // 通过通知发送导航请求
        // 注意：只有当前TabBar选中的页面会响应此通知，防止重复push
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            NotificationCenter.default.post(
                name: NSNotification.Name("NavigateToViewControllerNotification"),
                object: viewController
            )
        }
    }
    
    // 新增：根据状态更新UI的方法
    private func updateUIForState() {
        CATransaction.begin()
        CATransaction.setDisableActions(true)

        if AuthManager.shared.isLoggedIn {
            // --- 已登录状态 ---
            loginPromptView.isHidden = true
            usernameLabel.isHidden = false
            followersLabel.isHidden = false
            avatarContainer.isHidden = false // 确保头像容器可见
            // 恢复渐变边框
            gradientBorderLayer.isHidden = false

            // 恢复头部背景和蒙版
            // 注意：不在这里调用 updateHeaderBackgroundImage()，避免在API数据返回前设置错误的背景
            // 背景图会在 updateUserInfoUI() 中根据API数据正确设置
            headerBackgroundOverlay.isHidden = false

            // 根据是否有收藏数据显示收藏区域 (使用 hasFavorites 标志)
            favoritesButton.isHidden = !hasFavorites
            favoritesCollectionView.isHidden = !hasFavorites
            gradientMaskView.isHidden = !hasFavorites // 收藏列表的渐变蒙版

            // 调整菜单按钮容器的顶部约束
            menuStackView.snp.remakeConstraints { make in
                make.left.equalTo(30)
                make.right.equalToSuperview().offset(-30) // 左右边距30
                if hasFavorites {
                    make.top.equalTo(favoritesCollectionView.snp.bottom).offset(35) // 调整间距
                } else {
                    // 没有收藏，放在粉丝标签下方
                    make.top.equalTo(followersLabel.snp.bottom).offset(40) // 调整与粉丝标签的间距
                }
                let bottomSafeAreaInset = WindowUtil.safeAreaBottom
                make.bottom.lessThanOrEqualTo(containerView).offset(-(bottomSafeAreaInset + 20))
            }

        } else {
            // --- 未登录状态 ---
            loginPromptView.isHidden = false
            containerView.bringSubviewToFront(loginPromptView) // 确保在最前面
            usernameLabel.isHidden = true
            followersLabel.isHidden = true
            favoritesButton.isHidden = true
            favoritesCollectionView.isHidden = true
            gradientMaskView.isHidden = true // 隐藏收藏列表的渐变蒙版
            hasFavorites = false // 未登录状态，重置收藏标记

            // 重置用户昵称和收藏夹按钮标题
            usernameLabel.text = "点击登录"
            updateFavoritesButtonTitle(totalCount: 0)

            // 切换为未登录头像，隐藏渐变边框
            avatarImageView.image = UIImage(named: "default_avatar") // 使用未登录头像
            avatarContainer.isHidden = false // 头像容器仍然可见，但边框隐藏
            gradientBorderLayer.isHidden = true
            avatarMask.backgroundColor = .clear // 让未登录头像直接显示，不需要白色遮罩

            // 头部背景改为纯白，隐藏蒙版
            headerBackgroundImageView.image = nil
            headerBackgroundImageView.backgroundColor = UIColor(hex: "#F5F5F5")
            headerBackgroundOverlay.isHidden = true

            // 调整菜单按钮容器的顶部约束，使其紧贴头像下方
            menuStackView.snp.remakeConstraints { make in
                make.left.equalTo(30)
                make.right.equalToSuperview().offset(-30) // 左右边距30
                // 未登录时，菜单顶部相对于头像底部
                make.top.equalTo(avatarContainer.snp.bottom).offset(75) // 调整间距
                let bottomSafeAreaInset = WindowUtil.safeAreaBottom
                make.bottom.lessThanOrEqualTo(containerView).offset(-(bottomSafeAreaInset + 20))
            }
        }

        // 强制布局更新
        view.layoutIfNeeded()
        // 重新调整按钮高度（如果需要，因为约束可能改变了可用空间）
        adjustButtonHeights()

        CATransaction.commit()
    }
    
    // 调整按钮高度方法
    private func adjustButtonHeights() {
        // 计算可用空间 (减去底部安全区域)
        let bottomSafeAreaInset = WindowUtil.safeAreaBottom
        let totalAvailableHeight = containerView.bounds.height -
                                  (headerView.frame.height +
                                   favoritesCollectionView.frame.height +
                                   menuStackView.frame.origin.y -
                                   favoritesCollectionView.frame.origin.y +
                                   bottomSafeAreaInset + 20) // 底部安全距离 + 额外底部间距
        
        // 计算所需的总高度 (所有按钮高度 + 设置按钮上方的额外间距)
        let defaultButtonHeight = 50.0
        let settingsMargin = 25.0
        let requiredHeight = (defaultButtonHeight * CGFloat(menuItems.count)) + settingsMargin
        
        // 如果空间不足，计算调整后的按钮高度
        let buttonHeight: CGFloat
        if totalAvailableHeight < requiredHeight {
            // 计算调整后的按钮高度 (最小30pt)
            buttonHeight = max(30, (totalAvailableHeight - settingsMargin) / CGFloat(menuItems.count))
        } else {
            buttonHeight = defaultButtonHeight
        }
        
        // 更新所有按钮的高度
        var buttonCount = 0
        for view in menuStackView.arrangedSubviews {
            if let button = view as? UIButton {
                button.snp.updateConstraints { make in
                    make.height.equalTo(buttonHeight)
                }
                buttonCount += 1
            }
        }
        
        // 更新菜单容器底部约束，确保有足够的底部间距
        menuStackView.snp.updateConstraints { make in
            make.bottom.lessThanOrEqualTo(containerView).offset(-(bottomSafeAreaInset + 20))
        }
    }
    
    // 单独的方法设置渐变蒙版
    private func setupGradientMaskView() {
        // 创建渐变蒙版
        gradientMaskView = UIView()
        gradientMaskView.backgroundColor = .clear
        
        // 创建渐变层
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            UIColor.white.withAlphaComponent(0.01).cgColor,
            UIColor(hex: "#F5F5F5").cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0.5)
        gradientLayer.endPoint = CGPoint(x: 1, y: 0.5)
        gradientMaskView.layer.addSublayer(gradientLayer)
        
        // 添加到视图层次结构中，确保在收藏列表之上
        containerView.addSubview(gradientMaskView)
        containerView.bringSubviewToFront(gradientMaskView)
        
        // 设置约束
        gradientMaskView.snp.makeConstraints { make in
            make.right.equalToSuperview()
            make.top.bottom.equalTo(favoritesCollectionView)
            make.width.equalTo(50)
        }
        
        // 确保渐变层填满整个视图
        DispatchQueue.main.async {
            gradientLayer.frame = self.gradientMaskView.bounds
        }
    }
    
    // 查找HomeViewController的辅助方法
    private func findHomeViewController() -> HomeViewController? {
        // 寻找CustomTabBarController
        guard let tabBarController = findCustomTabBarController() else { return nil }
        
        // 遍历TabBarController的视图控制器
        for viewController in tabBarController.viewControllers ?? [] {
            // 检查当前控制器是否是导航控制器，且根控制器是HomeViewController
            if let navController = viewController as? UINavigationController,
               let homeVC = navController.viewControllers.first as? HomeViewController {
                return homeVC
            }
            
            // 直接检查当前控制器是否是HomeViewController
            if let homeVC = viewController as? HomeViewController {
                return homeVC
            }
        }
        
        return nil
    }
    
    // Helper function to find the CustomTabBarController
    private func findCustomTabBarController() -> CustomTabBarController? {
        var keyWindow: UIWindow? = nil
        if #available(iOS 15.0, *) {
            keyWindow = UIApplication.shared.connectedScenes
                .filter { $0.activationState == .foregroundActive }
                .first(where: { $0 is UIWindowScene })
                .flatMap({ $0 as? UIWindowScene })?.windows
                .first(where: { $0.isKeyWindow })
        } else {
            keyWindow = UIApplication.shared.windows.first(where: { $0.isKeyWindow })
        }
        return keyWindow?.rootViewController as? CustomTabBarController
    }

    // 在viewWillAppear中确保蒙版可见
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)

        print("LeftMenuViewController viewWillAppear: Disabling TabBar interaction")
        // 禁用 TabBar 交互
        findCustomTabBarController()?.disableInteraction()

        // 禁用隐式动画
        CATransaction.begin()
        CATransaction.setDisableActions(true)
        
        // 确保蒙版在最前面并且可见
        if let gradientMaskView = self.gradientMaskView {
            containerView.bringSubviewToFront(gradientMaskView)
            gradientMaskView.isHidden = false
            gradientMaskView.alpha = 1.0
            
            // 强制更新渐变层的frame
            if let gradientLayer = gradientMaskView.layer.sublayers?.first as? CAGradientLayer {
                gradientLayer.frame = gradientMaskView.bounds
            }
        }
        
        // 更新头部背景渐变层的尺寸
        if let gradientLayer = headerBackgroundOverlay.layer.sublayers?.first as? CAGradientLayer {
            gradientLayer.frame = headerBackgroundOverlay.bounds
        }
        
        CATransaction.commit()
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        
        // 禁用隐式动画
        CATransaction.begin()
        CATransaction.setDisableActions(true)
        
        // 再次确保所有渐变层的尺寸正确
        // 更新头部背景渐变层的尺寸
        if let gradientLayer = headerBackgroundOverlay.layer.sublayers?.first as? CAGradientLayer {
            gradientLayer.frame = headerBackgroundOverlay.bounds
        }
        
        // 更新收藏列表渐变蒙版
        if let gradientLayer = gradientMaskView.layer.sublayers?.first as? CAGradientLayer {
            gradientLayer.frame = gradientMaskView.bounds
        }
        
        // 更新头像渐变边框
        gradientBorderLayer.frame = CGRect(x: 0, y: 0, width: 64, height: 64)
        
        CATransaction.commit()
    }
    
    // 对象销毁时启用 TabBar 交互
    deinit {
        print("LeftMenuViewController deinit: Enabling TabBar interaction")
    }
    
    //视图消失
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        self.findCustomTabBarController()?.enableInteraction()
        
    }

    // 新增：获取用户数据的方法
    private func fetchUserData() {
        // 调用 API 获取用户信息
        // 假设有一个 API 服务类 ApiService
        APIManager.shared.getUserMain { [weak self] result in
            guard let self = self else { return }
            switch result {
            case .success(let userProfile):
                // 更新UI
                DispatchQueue.main.async {
                    if let userInfoData = userProfile.data {
                        self.updateUserInfoUI(profile: userInfoData)
                        // 成功获取用户信息后，获取收藏列表
                        self.fetchUserCollections()
                    } else {
                        // 处理 userProfile.data 为 nil 的情况，可能登出或显示错误
                        print("获取用户信息成功，但数据为空")
                        AuthManager.shared.logout() // 示例：数据异常则登出
                        self.updateUIForState()
                    }
                }
            case .failure(let error):
                print("获取用户信息失败: \(error)")
                // 可以显示错误提示或处理未登录状态
                // AuthManager.shared.logout() // 如果获取失败认为是未登录？
                DispatchQueue.main.async {
                    self.updateUIForState()
                }
            }
        }
    }

    // 新增：获取用户收藏列表的方法
    private func fetchUserCollections(page: Int = 0, size: Int = 10) {
        APIManager.shared.getUserWorksCollect(page: page, size: size) { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    // 优先使用列表是否为空判断，避免 total 字段不准确导致 UI 隐藏
                    let items = response.data?.list ?? []
                    self.favoriteItems = items
                    self.hasFavorites = !items.isEmpty

                    let totalCollections = response.data?.total ?? 0
                    print("获取收藏列表成功，总数字段: \(totalCollections), 实际列表数量: \(items.count)")

                    // 更新收藏夹按钮标题，显示实际的收藏总数
                    self.updateFavoritesButtonTitle(totalCount: totalCollections)

                    // 刷新收藏列表视图
                    self.favoritesCollectionView.reloadData()
                    // 更新整体UI状态（会根据 hasFavorites 调整布局）
                    self.updateUIForState()
                case .failure(let error):
                    print("获取用户收藏列表失败: \(error)")
                    self.hasFavorites = false // 获取失败，认为没有收藏
                    self.favoriteItems = [] // 获取失败也清空
                    // 更新收藏夹按钮标题为0
                    self.updateFavoritesButtonTitle(totalCount: 0)
                    self.favoritesCollectionView.reloadData()
                    self.updateUIForState() // 更新UI状态
                }
            }
        }
    }

    // 新增：根据获取的用户信息更新特定UI元素
    private func updateUserInfoUI(profile: UserInfoData) {
        usernameLabel.text = profile.customerName
        followersLabel.text = "粉丝: \(profile.fansNumber)"
        avatarImageView.kf.setImage(with: URL(string: profile.wxAvator), placeholder: UIImage(named: "default_avatar"))

        // 设置头部背景图：优先使用背景图，没有背景图时使用头像作为背景
        if let backgroundImage = profile.backgroundImage, !backgroundImage.isEmpty, let backgroundURL = URL(string: backgroundImage) {
            // 有背景图，使用背景图
            headerBackgroundImageView.kf.setImage(
                with: backgroundURL,
                placeholder: nil,
                options: [
                    .transition(.fade(0.2)),
                    .cacheOriginalImage
                ]
            )
        } else if !profile.wxAvator.isEmpty, let avatarURL = URL(string: profile.wxAvator) {
            // 没有背景图但有头像，使用头像作为背景
            headerBackgroundImageView.kf.setImage(
                with: avatarURL,
                placeholder: nil,
                options: [
                    .transition(.fade(0.2)),
                    .cacheOriginalImage
                ]
            )
        } else {
            // 既没有背景图也没有头像，设置为默认背景色
            headerBackgroundImageView.image = nil
            headerBackgroundImageView.backgroundColor = UIColor(hex: "#F5F5F5")
        }
    }

    // 新增：更新收藏夹按钮标题的方法
    private func updateFavoritesButtonTitle(totalCount: Int) {
        var configuration = favoritesButton.configuration
        configuration?.title = "收藏夹 \(totalCount)"
        favoritesButton.configuration = configuration
    }

    /// 在收起侧边栏后以全屏模态方式呈现 VC
    private func presentModally(_ viewController: UIViewController) {
        viewController.modalPresentationStyle = .fullScreen
        // 关闭侧边栏后再 present，避免动画冲突
        dismissSideMenu()
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            if let presenter = self.findCustomTabBarController() {
                presenter.present(viewController, animated: true)
            } else if let root = UIApplication.shared.windows.first(where: { $0.isKeyWindow })?.rootViewController {
                root.present(viewController, animated: true)
            }
        }
    }
}

// 添加 UICollectionViewDelegate 和 UICollectionViewDataSource 扩展
extension LeftMenuViewController: UICollectionViewDelegate, UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        // 使用 AuthManager 判断登录状态
        return AuthManager.shared.isLoggedIn ? favoriteItems.count : 0
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "FavoriteCell", for: indexPath) as! FavoriteCell
        if indexPath.item < favoriteItems.count {
            let item = favoriteItems[indexPath.item]
            cell.configure(with: item)
        }
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        guard indexPath.item < favoriteItems.count else { return }
        let playerVC = VideoDisplayCenterViewController(
            videoList: favoriteItems,
            startIndex: indexPath.item,
            hideNavBackButton: false,
            showCustomNavBar: true,
            needsTabBarOffset: false
        )
        presentModally(playerVC)
    }
}

// 收藏单元格
class FavoriteCell: UICollectionViewCell {
    // 封面图
    private let coverImageView: UIImageView = {
        let iv = UIImageView()
        iv.contentMode = .scaleAspectFill
        iv.clipsToBounds = true
        iv.layer.cornerRadius = 12
        return iv
    }()

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        // 设置圆角 & 阴影背景色
        contentView.layer.cornerRadius = 12
        contentView.layer.masksToBounds = true
        contentView.addSubview(coverImageView)
        coverImageView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            coverImageView.topAnchor.constraint(equalTo: contentView.topAnchor),
            coverImageView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            coverImageView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            coverImageView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor)
        ])
    }

    /// 使用 VideoItem 配置 Cell
    func configure(with item: VideoItem) {
        if let urlString = item.worksCoverImg, let url = URL(string: urlString) {
            coverImageView.kf.setImage(with: url, placeholder: UIImage(named: "video_placeholder"))
        } else {
            coverImageView.image = UIImage(named: "video_placeholder")
        }
    }
}
