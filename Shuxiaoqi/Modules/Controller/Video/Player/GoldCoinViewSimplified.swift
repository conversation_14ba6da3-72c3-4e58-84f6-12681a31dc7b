//
//  GoldCoinViewSimplified.swift
//  Shuxiaoqi
//
//  Created by Augment Agent on 2025/9/12.
//

import UIKit
import QuartzCore
import SnapKit

/// 简化的金币控件 - 完全服务器驱动
/// 40*40大小，透明黑灰色圆形背景，中间30*30金币图片，外围#E9D65A色进度条
/// 移除所有本地计算，完全依赖服务器API返回的进度
class GoldCoinViewSimplified: UIView {
    
    // MARK: - UI Components
    private let backgroundView = UIView()
    private let coinImageView = UIImageView()
    private let progressLayer = CAShapeLayer()
    
    // MARK: - Properties
    private var isAnimating = false
    private var shouldShowProgress = true
    private var isLoggedIn = false
    private var isPlaying = false
    
    // 简化：完全依赖服务器驱动
    private var globalTaskCurrentSeconds: Int = 0      // 当前进度（从服务器获取）
    private var globalTaskTotalSeconds: Int = 120      // 总需要秒数（从配置获取）
    private var globalTaskProgress: Double = 0.0       // 进度百分比
    private var hasGlobalTaskConfig: Bool = false      // 是否已加载配置
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - Setup
    private func setupUI() {
        // 设置背景圆形视图
        backgroundView.backgroundColor = UIColor.black.withAlphaComponent(0.3)
        backgroundView.layer.cornerRadius = 20 // 40*40的一半
        addSubview(backgroundView)
        
        // 设置金币图片
        coinImageView.image = UIImage(named: "video_gold_coin")
        coinImageView.contentMode = .scaleAspectFit
        addSubview(coinImageView)
        
        // 设置进度条
        setupProgressLayer()
        
        // 设置约束
        setupConstraints()
    }
    
    private func setupProgressLayer() {
        // 创建圆形路径
        let center = CGPoint(x: 20, y: 20) // 40*40的中心点
        let radius: CGFloat = 19 // 半径稍小于背景圆形
        let startAngle = -CGFloat.pi / 2 // 从顶部开始
        let endAngle = startAngle + 2 * CGFloat.pi // 完整圆形
        
        let circularPath = UIBezierPath(arcCenter: center,
                                       radius: radius,
                                       startAngle: startAngle,
                                       endAngle: endAngle,
                                       clockwise: true)
        
        progressLayer.path = circularPath.cgPath
        progressLayer.strokeColor = UIColor(hex: "#E9D65A").cgColor
        progressLayer.lineWidth = 2.0
        progressLayer.fillColor = UIColor.clear.cgColor
        progressLayer.lineCap = .round
        progressLayer.strokeStart = 0
        progressLayer.strokeEnd = 0
        
        layer.addSublayer(progressLayer)
    }
    
    private func setupConstraints() {
        backgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        coinImageView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.size.equalTo(30)
        }
    }
    
    // MARK: - Public Methods

    /// 设置登录状态
    func setupGoldCoin(isUserLoggedIn: Bool) {
        self.isLoggedIn = isUserLoggedIn
        isHidden = false

        if isLoggedIn {
            // 已登录：显示当前任务进度
            updateProgressDisplay()
        } else {
            // 未登录：直接展示满圈（静态）
            progressLayer.removeAllAnimations()
            progressLayer.isHidden = false
            progressLayer.strokeEnd = 1.0
            isAnimating = false
        }
        
        print("[GoldCoinSimplified] 设置登录状态: \(isLoggedIn)")
    }

    /// 开始播放（仅设置状态，不做本地计算）
    func startPlaying() {
        guard isLoggedIn else { return }
        isPlaying = true
        print("[GoldCoinSimplified] 开始播放")
    }

    /// 暂停播放
    func pausePlaying() {
        guard isLoggedIn else { return }
        isPlaying = false
        print("[GoldCoinSimplified] 暂停播放")
    }

    /// 拖动进度条时调用（仅记录状态）
    func seekTo(time: TimeInterval) {
        guard isLoggedIn else { return }
        print("[GoldCoinSimplified] 拖动进度条")
    }

    /// 更新进度条显示（完全依赖服务器数据）
    private func updateProgressDisplay() {
        guard isLoggedIn else { return }

        progressLayer.isHidden = false

        if hasGlobalTaskConfig {
            // 直接使用服务器返回的进度，不做任何本地计算
            let progress = min(max(globalTaskProgress, 0.0), 1.0)
            
            // 使用动画更新进度，让变化更平滑
            CATransaction.begin()
            CATransaction.setAnimationDuration(0.3)
            progressLayer.strokeEnd = CGFloat(progress)
            CATransaction.commit()
            
            print("[GoldCoinSimplified] 更新进度: \(String(format: "%.1f", progress * 100))% (\(globalTaskCurrentSeconds)/\(globalTaskTotalSeconds))")
        } else {
            // 没有配置时显示空进度
            progressLayer.strokeEnd = 0
        }
    }

    /// 设置是否显示进度条
    func setProgressVisible(_ visible: Bool) {
        shouldShowProgress = visible
        if !visible { 
            progressLayer.isHidden = true 
        }
    }

    /// 设置金币图片
    func setCoinImage(_ image: UIImage?) {
        coinImageView.image = image
    }

    /// 获取当前进度百分比
    func getCurrentProgress() -> Double { 
        return globalTaskProgress 
    }

    /// 停止所有动画
    func stopAll() {
        progressLayer.removeAllAnimations()
        isAnimating = false
        isPlaying = false
        print("[GoldCoinSimplified] 停止所有动画")
    }

    // MARK: - Server-Driven Methods

    /// 更新全局任务配置（完全服务器驱动）
    /// - Parameters:
    ///   - totalSeconds: 总秒数
    ///   - currentSeconds: 当前秒数
    func updateGlobalTaskConfig(totalSeconds: Int, currentSeconds: Int) {
        print("[GoldCoinSimplified] 服务器更新: \(currentSeconds)/\(totalSeconds)")
        
        self.globalTaskTotalSeconds = max(1, totalSeconds)
        self.globalTaskCurrentSeconds = min(max(0, currentSeconds), self.globalTaskTotalSeconds)
        self.globalTaskProgress = Double(self.globalTaskCurrentSeconds) / Double(self.globalTaskTotalSeconds)
        self.hasGlobalTaskConfig = totalSeconds > 0
        
        // 立即更新UI显示
        updateProgressDisplay()
        
        // 如果进度达到100%，播放完成动画
        if globalTaskProgress >= 1.0 {
            animateCompletion()
        }
    }

    /// 播放完成动画
    private func animateCompletion() {
        // 进度条闪烁效果
        let flashAnimation = CABasicAnimation(keyPath: "opacity")
        flashAnimation.fromValue = 1.0
        flashAnimation.toValue = 0.3
        flashAnimation.duration = 0.3
        flashAnimation.autoreverses = true
        flashAnimation.repeatCount = 3

        progressLayer.add(flashAnimation, forKey: "completionFlash")
        print("[GoldCoinSimplified] 播放完成动画")
    }

    /// 获取当前全局任务信息
    func getCurrentGlobalTaskInfo() -> (progress: Double, serverSeconds: Int, totalSeconds: Int, hasConfig: Bool) {
        return (globalTaskProgress, globalTaskCurrentSeconds, globalTaskTotalSeconds, hasGlobalTaskConfig)
    }

    /// 获取当前全局观看时间
    func getCurrentGlobalWatchTime() -> TimeInterval {
        return TimeInterval(globalTaskCurrentSeconds)
    }

    /// 获取当前播放状态
    var isCurrentlyPlaying: Bool {
        return isPlaying
    }

    /// 设置视频已完整观看状态
    func setVideoCompleted() {
        isPlaying = false
        print("[GoldCoinSimplified] 视频已完整观看")
    }
}

// MARK: - UIColor Extension
extension UIColor {
    convenience init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            alpha: Double(a) / 255
        )
    }
}
