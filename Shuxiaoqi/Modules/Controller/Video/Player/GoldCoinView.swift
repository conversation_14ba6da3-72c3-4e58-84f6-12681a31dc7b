//
//  GoldCoinView.swift
//  Shuxia<PERSON>qi
//
//  Created by yong<PERSON><PERSON> ye on 2025/8/11.
//  Updated by Augment Agent on 2025/9/12 - 简化为完全服务器驱动版本
//

import UIKit
import QuartzCore
import SnapKit

/// 金币控件 - 完全服务器驱动，添加平滑动画
/// 40*40大小，透明黑灰色圆形背景，中间30*30金币图片，外围#E9D65A色进度条
/// 移除所有本地计算，完全依赖服务器API返回的进度，添加平滑动画消除格子感
class GoldCoinView: UIView {
    
    // MARK: - UI Components
    private let backgroundView = UIView()
    private let coinImageView = UIImageView()
    private let progressLayer = CAShapeLayer()
    
    // MARK: - Properties
    private var isAnimating = false
    private var shouldShowProgress = true
    private var isLoggedIn = false
    private var isPlaying = false
    
    // 服务器驱动的进度数据
    private var globalTaskCurrentSeconds: Int = 0      // 当前进度（从服务器获取）
    private var globalTaskTotalSeconds: Int = 120      // 总需要秒数（从配置获取）
    private var globalTaskProgress: Double = 0.0       // 进度百分比
    private var hasGlobalTaskConfig: Bool = false      // 是否已加载配置
    
    // 平滑动画相关
    private var displayedProgress: Double = 0.0        // 当前显示的进度
    private var targetProgress: Double = 0.0           // 目标进度（服务器返回）
    private var displayLink: CADisplayLink?            // 用于平滑动画
    private var animationStartTime: CFTimeInterval = 0 // 动画开始时间
    private var animationDuration: CFTimeInterval = 4.0 // 动画持续时间（4秒，更慢的推进）

    // 时钟式动画相关
    private var isCompletingCycle: Bool = false        // 是否正在完成一个周期
    private var pendingNewProgress: Double = 0.0       // 等待显示的新进度
    private var hasPendingReset: Bool = false          // 是否有待处理的重置
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - Setup
    private func setupUI() {
        // 设置背景圆形视图
        backgroundView.backgroundColor = UIColor.black.withAlphaComponent(0.3)
        backgroundView.layer.cornerRadius = 20 // 40*40的一半
        addSubview(backgroundView)
        
        // 设置金币图片
        coinImageView.image = UIImage(named: "video_gold_coin")
        coinImageView.contentMode = .scaleAspectFit
        addSubview(coinImageView)
        
        // 设置进度条
        setupProgressLayer()
        
        // 设置约束
        setupConstraints()
    }
    
    private func setupProgressLayer() {
        // 创建圆形路径
        let center = CGPoint(x: 20, y: 20) // 40*40的中心点
        let radius: CGFloat = 19 // 半径稍小于背景圆形
        let startAngle = -CGFloat.pi / 2 // 从顶部开始
        let endAngle = startAngle + 2 * CGFloat.pi // 完整圆形
        
        let circularPath = UIBezierPath(arcCenter: center,
                                       radius: radius,
                                       startAngle: startAngle,
                                       endAngle: endAngle,
                                       clockwise: true)
        
        progressLayer.path = circularPath.cgPath
        progressLayer.strokeColor = UIColor(hex: "#E9D65A").cgColor
        progressLayer.lineWidth = 2.0
        progressLayer.fillColor = UIColor.clear.cgColor
        progressLayer.lineCap = .round
        progressLayer.strokeStart = 0
        progressLayer.strokeEnd = 0
        
        layer.addSublayer(progressLayer)
    }
    
    private func setupConstraints() {
        backgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        coinImageView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.size.equalTo(30)
        }
    }
    
    // MARK: - Public Methods

    /// 设置登录状态
    func setupGoldCoin(isUserLoggedIn: Bool) {
        self.isLoggedIn = isUserLoggedIn
        isHidden = false

        if isLoggedIn {
            // 已登录：显示当前任务进度
            updateProgressDisplay()
        } else {
            // 未登录：直接展示满圈（静态）
            progressLayer.removeAllAnimations()
            progressLayer.isHidden = false
            progressLayer.strokeEnd = 1.0
            isAnimating = false
        }
        
        print("[GoldCoinView] 设置登录状态: \(isLoggedIn)")
    }

    /// 开始播放（仅设置状态，不做本地计算）
    func startPlaying() {
        guard isLoggedIn else { return }
        isPlaying = true
        print("[GoldCoinView] 开始播放")
    }

    /// 暂停播放
    func pausePlaying() {
        guard isLoggedIn else { return }
        isPlaying = false
        print("[GoldCoinView] 暂停播放")
    }

    /// 拖动进度条时调用（仅记录状态）
    func seekTo(time: TimeInterval) {
        guard isLoggedIn else { return }
        print("[GoldCoinView] 拖动进度条")
    }

    /// 设置是否显示进度条
    func setProgressVisible(_ visible: Bool) {
        shouldShowProgress = visible
        if !visible { 
            progressLayer.isHidden = true 
        }
    }

    /// 设置金币图片
    func setCoinImage(_ image: UIImage?) {
        coinImageView.image = image
    }

    /// 获取当前进度百分比
    func getCurrentProgress() -> Double { 
        return globalTaskProgress 
    }

    /// 停止所有动画
    func stopAll() {
        progressLayer.removeAllAnimations()
        stopSmoothAnimation()

        // 重置时钟式动画状态
        isCompletingCycle = false
        hasPendingReset = false
        pendingNewProgress = 0.0

        isAnimating = false
        isPlaying = false
        print("[GoldCoinView] 停止所有动画")
    }

    /// 获取当前全局任务信息
    func getCurrentGlobalTaskInfo() -> (progress: Double, serverSeconds: Int, totalSeconds: Int, hasConfig: Bool) {
        return (globalTaskProgress, globalTaskCurrentSeconds, globalTaskTotalSeconds, hasGlobalTaskConfig)
    }

    /// 获取当前全局观看时间
    func getCurrentGlobalWatchTime() -> TimeInterval {
        return TimeInterval(globalTaskCurrentSeconds)
    }

    /// 获取当前播放状态
    var isCurrentlyPlaying: Bool {
        return isPlaying
    }

    /// 设置视频已完整观看状态
    func setVideoCompleted() {
        isPlaying = false
        print("[GoldCoinView] 视频已完整观看")
    }

    // MARK: - Server-Driven Methods

    /// 更新全局任务配置（服务器驱动 + 时钟式动画）
    /// - Parameters:
    ///   - totalSeconds: 总秒数
    ///   - currentSeconds: 当前秒数
    func updateGlobalTaskConfig(totalSeconds: Int, currentSeconds: Int) {
        print("[GoldCoinView] 服务器更新: \(currentSeconds)/\(totalSeconds)")

        let oldProgress = globalTaskProgress
        let oldTotal = globalTaskTotalSeconds

        self.globalTaskTotalSeconds = max(1, totalSeconds)
        self.globalTaskCurrentSeconds = min(max(0, currentSeconds), self.globalTaskTotalSeconds)
        self.globalTaskProgress = Double(self.globalTaskCurrentSeconds) / Double(self.globalTaskTotalSeconds)
        self.hasGlobalTaskConfig = totalSeconds > 0

        // 检测是否是新的奖励周期（总时长变化或进度大幅倒退）
        let isNewRewardCycle = (totalSeconds != oldTotal) || (globalTaskProgress < oldProgress - 0.1)

        if isNewRewardCycle && oldProgress > 0.8 {
            // 新奖励周期：先完成当前周期，再开始新周期
            print("[GoldCoinView] 检测到新奖励周期，执行时钟式动画")
            handleNewRewardCycle(newProgress: globalTaskProgress)
        } else {
            // 正常进度更新
            let progressDiff = abs(globalTaskProgress - oldProgress)
            if progressDiff > 0.001 {
                // 调整动画时长：更慢的推进
                if progressDiff > 0.1 {
                    animationDuration = 5.0  // 大跳跃用5秒
                } else if progressDiff > 0.05 {
                    animationDuration = 4.5  // 中等跳跃用4.5秒
                } else {
                    animationDuration = 4.0  // 小跳跃用4秒
                }

                updateProgressDisplay()
            }
        }

        print("[GoldCoinView] 进度变化: \(String(format: "%.1f", oldProgress * 100))% → \(String(format: "%.1f", globalTaskProgress * 100))% (动画时长: \(animationDuration)s)")
    }

    /// 处理新奖励周期的时钟式动画
    private func handleNewRewardCycle(newProgress: Double) {
        // 如果正在执行周期动画，先停止
        if isCompletingCycle {
            return
        }

        // 标记正在完成周期
        isCompletingCycle = true
        pendingNewProgress = newProgress
        hasPendingReset = true

        // 先完成当前周期到100%
        targetProgress = 1.0
        animationDuration = 2.0  // 完成周期用2秒
        startSmoothAnimation()

        print("[GoldCoinView] 开始时钟式动画：先完成到100%，然后重置到\(String(format: "%.1f", newProgress * 100))%")
    }

    /// 更新进度条显示（服务器驱动 + 平滑动画）
    private func updateProgressDisplay() {
        guard isLoggedIn else { return }

        progressLayer.isHidden = false

        if hasGlobalTaskConfig {
            // 设置目标进度
            let newTargetProgress = min(max(globalTaskProgress, 0.0), 1.0)

            // 如果目标进度有变化，启动平滑动画
            if abs(newTargetProgress - targetProgress) > 0.001 {
                targetProgress = newTargetProgress
                startSmoothAnimation()
            }

            print("[GoldCoinView] 更新进度: \(String(format: "%.1f", targetProgress * 100))% (\(globalTaskCurrentSeconds)/\(globalTaskTotalSeconds))")
        } else {
            // 没有配置时显示空进度
            targetProgress = 0
            displayedProgress = 0
            progressLayer.strokeEnd = 0
            stopSmoothAnimation()
        }
    }

    /// 启动平滑动画
    private func startSmoothAnimation() {
        // 停止之前的动画
        stopSmoothAnimation()

        // 记录动画开始时间和起始进度
        animationStartTime = CACurrentMediaTime()
        let startProgress = displayedProgress

        // 创建DisplayLink进行平滑插值
        displayLink = CADisplayLink(target: self, selector: #selector(updateSmoothProgress))
        displayLink?.add(to: .main, forMode: .common)

        print("[GoldCoinView] 启动平滑动画: \(String(format: "%.1f", startProgress * 100))% → \(String(format: "%.1f", targetProgress * 100))%")
    }

    /// 停止平滑动画
    private func stopSmoothAnimation() {
        displayLink?.invalidate()
        displayLink = nil
    }

    /// 更新平滑进度
    @objc private func updateSmoothProgress() {
        let currentTime = CACurrentMediaTime()
        let elapsed = currentTime - animationStartTime
        let progress = min(elapsed / animationDuration, 1.0)

        // 使用缓动函数让动画更自然
        let easedProgress = easeInOutQuad(progress)

        // 计算当前显示进度
        let startProgress = displayedProgress
        let progressDiff = targetProgress - startProgress
        displayedProgress = startProgress + progressDiff * easedProgress

        // 更新UI
        progressLayer.strokeEnd = CGFloat(displayedProgress)

        // 动画完成
        if progress >= 1.0 {
            displayedProgress = targetProgress
            progressLayer.strokeEnd = CGFloat(targetProgress)
            stopSmoothAnimation()

            // 检查是否需要处理周期完成
            if isCompletingCycle && targetProgress >= 1.0 {
                handleCycleCompletion()
            } else if targetProgress >= 1.0 && !isCompletingCycle {
                // 正常的100%完成动画
                animateCompletion()
            }
        }
    }

    /// 处理周期完成后的重置
    private func handleCycleCompletion() {
        print("[GoldCoinView] 周期完成，开始重置动画")

        // 播放完成动画
        animateCompletion()

        // 延迟1秒后开始重置动画
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
            self?.startResetAnimation()
        }
    }

    /// 开始重置动画
    private func startResetAnimation() {
        guard hasPendingReset else { return }

        print("[GoldCoinView] 开始重置到新进度: \(String(format: "%.1f", pendingNewProgress * 100))%")

        // 重置状态
        isCompletingCycle = false
        hasPendingReset = false

        // 立即重置显示进度到0
        displayedProgress = 0.0
        progressLayer.strokeEnd = 0.0

        // 设置新的目标进度
        targetProgress = pendingNewProgress
        animationDuration = 4.0  // 重置后的动画用4秒

        // 延迟0.5秒后开始新进度动画，让用户看到重置效果
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            self?.startSmoothAnimation()
        }
    }

    /// 缓动函数：更慢更平滑的动画
    private func easeInOutQuad(_ t: Double) -> Double {
        // 使用更平缓的缓动曲线
        if t < 0.5 {
            return 2 * t * t * t  // 更慢的开始
        } else {
            let f = t - 1
            return 1 + 2 * f * f * f  // 更慢的结束
        }
    }

    /// 播放完成动画
    private func animateCompletion() {
        // 进度条闪烁效果
        let flashAnimation = CABasicAnimation(keyPath: "opacity")
        flashAnimation.fromValue = 1.0
        flashAnimation.toValue = 0.3
        flashAnimation.duration = 0.3
        flashAnimation.autoreverses = true
        flashAnimation.repeatCount = 3

        progressLayer.add(flashAnimation, forKey: "completionFlash")
        print("[GoldCoinView] 播放完成动画")
    }
}
