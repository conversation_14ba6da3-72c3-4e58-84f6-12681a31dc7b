//
//  GoldCoinView.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/8/11.
//

import UIKit
import QuartzCore
import SnapKit

/// 金币控件
/// 40*40大小，透明黑灰色圆形背景，中间30*30金币图片，外围#E9D65A色进度条
/// 支持转圈动画（2秒一圈）和外部控制显示/隐藏进度条
class GoldCoinView: UIView {
    
    // MARK: - UI Components
    private let backgroundView = UIView()
    private let coinImageView = UIImageView()
    private let progressLayer = CAShapeLayer()
    private var rotationAnimation: CABasicAnimation?
    
    // MARK: - Properties
    private var isAnimating = false
    private var shouldShowProgress = true
    private var isLoggedIn = false
    private var isPlaying = false
    // 不再本地累计/平滑，完全以服务端返回为准（仅保留阶段切换的微动画）

    // 全局任务水桶机制（通过配置API获取）
    private var globalTaskCurrentSeconds: Int = 0      // 当前进度（从服务器获取）
    private var globalTaskTotalSeconds: Int = 120      // 总需要秒数（从配置获取）
    private var globalTaskProgress: Double = 0.0       // 进度百分比
    private var hasGlobalTaskConfig: Bool = false      // 是否已加载配置

    // 本地累计已移除

    // UI平滑（分阶段显示）：阶段从0开始（基线重置），避免奖励后出现回退错觉
    private var displayedStepBaselineSeconds: Int = 0 // 当前阶段内的服务器基线秒数（0...stepTotal）
    private var lastDisplayedStepSeconds: Int = 0     // 最近一次绘制的阶段内秒数

    // 阶段展示：每一阶段的起止（阶段总长等于 totalSeconds）
    private var currentStepStartSeconds: Int = 0      // 阶段起点（相对0）
    private var currentStepEndSeconds: Int = 0        // 阶段终点（相对total）

    // 显示平滑：使用 CADisplayLink 按“每秒 1/total”的速率线性插值，仅用于UI展示
    private var displayLink: CADisplayLink?
    private var lastServerProgressTime: CFTimeInterval = CACurrentMediaTime()

    // 是否在新阶段出现时执行“归零后快速填充”的过渡动画（默认开启，可关闭以避免任何回退感）
    private var resetFillOnNewStage: Bool = false

    // 回调（测试用途已废弃，不再触发）
    var onGoldCoinEarned: (() -> Void)?
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - Setup
    private func setupUI() {
        // 设置背景圆形视图
        backgroundView.backgroundColor = UIColor.black.withAlphaComponent(0.3)
        backgroundView.layer.cornerRadius = 20 // 40*40的一半
        addSubview(backgroundView)
        
        // 设置金币图片
        coinImageView.image = UIImage(named: "video_gold_coin")
        coinImageView.contentMode = .scaleAspectFit
        addSubview(coinImageView)
        
        // 设置进度条
        setupProgressLayer()
        
        // 设置约束
        setupConstraints()
    }
    
    private func setupProgressLayer() {
        // 创建圆形路径
        let center = CGPoint(x: 20, y: 20) // 40*40的中心点
        let radius: CGFloat = 19 // 半径稍小于背景圆形
        let startAngle = -CGFloat.pi / 2 // 从顶部开始
        let endAngle = startAngle + 2 * CGFloat.pi // 完整圆形
        
        let circularPath = UIBezierPath(arcCenter: center,
                                       radius: radius,
                                       startAngle: startAngle,
                                       endAngle: endAngle,
                                       clockwise: true)
        
        progressLayer.path = circularPath.cgPath
        progressLayer.strokeColor = UIColor(hex: "#E9D65A").cgColor
        progressLayer.lineWidth = 2.0
        progressLayer.fillColor = UIColor.clear.cgColor
        progressLayer.lineCap = .round
        progressLayer.strokeStart = 0
        progressLayer.strokeEnd = 0
        
        layer.addSublayer(progressLayer)
    }
    
    private func setupConstraints() {
        backgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        coinImageView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.size.equalTo(30)
        }
    }
    
    // MARK: - Public Methods

    /// 设置登录状态
    /// 需求：未登录也展示金币按钮，点击后在外部触发登录
    func setupGoldCoin(isUserLoggedIn: Bool) {
        self.isLoggedIn = isUserLoggedIn
        // 未登录情况下也显示控件，由外部点击手势处理登录跳转
        isHidden = false

        // 如果已经在播放中且有全局任务配置，不要停止计时器
        let shouldKeepTimer = isPlaying && hasGlobalTaskConfig

        if !shouldKeepTimer {
            // 停止之前的计时器
//            stopGoldCoinTimer()
        }

        if isLoggedIn {
            // 已登录：显示当前任务进度
            updateProgressDisplay()
        } else {
            // 未登录：直接展示满圈（静态），符合“未登录时展示一个满圈状态”的需求
            progressLayer.removeAllAnimations()
            progressLayer.isHidden = false
            progressLayer.strokeEnd = 1.0
            isAnimating = false
        }

        // concise logs only when needed; avoid noisy prints per second
    }

    /// 开始播放（已登录用户开始计时）
    /// 注意：现在计时由GoldCoinVideoWatchManager控制，这里只做UI更新
    func startPlaying() {
        guard isLoggedIn else { return }

        // 仅设置播放态（UI无需自增）
        isPlaying = true
        startDisplayLinkIfNeeded()
        // keep silent to avoid noisy logs
    }

    /// 暂停播放
    func pausePlaying() {
        guard isLoggedIn else { return }

        isPlaying = false
        stopDisplayLink()
        // keep silent to avoid noisy logs
    }

    /// 拖动进度条时调用（不影响全局时长，只是暂停/恢复计时）
    func seekTo(time: TimeInterval) {
        guard isLoggedIn else { return }

        // 不做本地累计，完全以服务端为准

        // keep silent to avoid noisy logs
    }

    /// 未登录用户的简单动画
    private func startUnloggedAnimation() {
        guard shouldShowProgress else { return }

        progressLayer.removeAllAnimations()
        progressLayer.strokeEnd = 0
        progressLayer.isHidden = false
        isAnimating = true

        let animation = CABasicAnimation(keyPath: "strokeEnd")
        animation.fromValue = 0
        animation.toValue = 1
        animation.duration = 2.0
        animation.timingFunction = CAMediaTimingFunction(name: .linear)
        animation.delegate = self

        progressLayer.add(animation, forKey: "unloggedAnimation")
        progressLayer.strokeEnd = 1
    }

    // 本地计时器与自增逻辑已移除，进度完全由后端值驱动

    /// 更新进度条显示
    private func updateProgressDisplay() {
        guard isLoggedIn else { return }

        progressLayer.isHidden = false

        if hasGlobalTaskConfig {
            // 若全局任务已完成，保持100%
            if globalTaskProgress >= 1.0 {
                progressLayer.strokeEnd = 1.0
                return
            }

            // 平滑渲染（钟表式）：仅按本地显示基线 + 时间插值前进；不向服务器进度“跳跃前进”。
            // 同时不超过服务器已确认的阶段内秒数（上限），确保不会“超前”。
            let stepTotal = max(1, currentStepEndSeconds - currentStepStartSeconds)

            // 服务器阶段上限（用于上限夹紧）
            let serverStepSeconds = max(0, min(globalTaskCurrentSeconds, stepTotal))
            let serverMax = Double(serverStepSeconds) / Double(stepTotal)

            // 本地基线（使用已显示的阶段秒数）
            let now = CACurrentMediaTime()
            let elapsed = max(0.0, now - lastServerProgressTime)
            let localBase = Double(lastDisplayedStepSeconds) / Double(stepTotal)
            var predicted = localBase + (elapsed / Double(stepTotal))

            // 夹紧：不超过服务器确认的进度，也不低于0/高于1
            predicted = min(predicted, serverMax)
            predicted = min(max(predicted, 0.0), 1.0)

            // 应用
            progressLayer.strokeEnd = CGFloat(predicted)
            lastDisplayedStepSeconds = Int(predicted * Double(stepTotal))
        } else {
            // 没有配置时显示转圈动画
            progressLayer.strokeEnd = 0
            // silent
        }
    }

    // 本地累计更新已移除

    /// 设置是否显示进度条（用于首页等无动画场景）
    func setProgressVisible(_ visible: Bool) {
        shouldShowProgress = visible
        if !visible { progressLayer.isHidden = true }
    }

    /// 设置金币图片
    func setCoinImage(_ image: UIImage?) {
        coinImageView.image = image
    }

    /// 获取当前进度百分比（用于调试）
    func getCurrentProgress() -> Double { globalTaskProgress }

    // 测试加时已废弃

    /// 停止所有动画和计时器
    func stopAll() {
        progressLayer.removeAllAnimations()
        isAnimating = false
        isPlaying = false
        stopDisplayLink()
        // 完全以服务端为准，无需本地累计
    }

    // MARK: - Global Task Methods

    /// 更新全局任务配置（阶段内）
    /// - Parameters:
    ///   - totalSeconds: 阶段总秒数（例如：下一阈值100 - 上一阈值90 = 10）
    ///   - currentSeconds: 阶段内当前秒数（例如：viewingSeconds 91 -> 1）
    func updateGlobalTaskConfig(totalSeconds: Int, currentSeconds: Int) {
        // 以阶段内值为准：进度 = currentSeconds / totalSeconds
        let prevTotal = self.globalTaskTotalSeconds
        let prevDisplayed = self.lastDisplayedStepSeconds
        let wasInitialized = self.hasGlobalTaskConfig

        self.globalTaskTotalSeconds = max(1, totalSeconds)
        self.hasGlobalTaskConfig = totalSeconds > 0
        let serverSeconds = min(max(0, currentSeconds), self.globalTaskTotalSeconds)
        self.globalTaskCurrentSeconds = serverSeconds
        self.globalTaskProgress = Double(self.globalTaskCurrentSeconds) / Double(self.globalTaskTotalSeconds)
        // 记录服务器进度时间点，用于显示插值
        self.lastServerProgressTime = CACurrentMediaTime()

        // 阶段边界：阶段从0开始到 totalSeconds 结束
        self.currentStepStartSeconds = 0
        self.currentStepEndSeconds = self.globalTaskTotalSeconds

        // 防回退与阶段切换策略：
        // - 侦测“阶段重置”：服务器阶段内秒数 < 之前本地显示（说明上一圈刚满，新圈开始）
        //   处理：立即清空到0（无倒退动画），从0重新走（像时钟）。
        // - 其他情况：不回退，基线取本地显示与服务器较大值。
        let serverStageSeconds = serverSeconds
        let isPlayingNow = self.isPlaying && GoldCoinVideoWatchManager.shared.isCurrentlyWatching
        let isNewTask = prevTotal != self.globalTaskTotalSeconds
        let stageReset = wasInitialized && (serverStageSeconds < prevDisplayed || isNewTask)

        // 调试日志：记录阶段切换检测
        if wasInitialized {
            print("[GoldCoinView] 阶段检测: 服务器=\(serverStageSeconds)s, 本地显示=\(prevDisplayed)s, 新任务=\(isNewTask), 重置=\(stageReset)")
        }

        if stageReset {
            // 清零显示，避免回退动画
            self.globalTaskCurrentSeconds = 0
            self.globalTaskProgress = 0
            self.displayedStepBaselineSeconds = 0
            self.lastDisplayedStepSeconds = 0
            self.lastServerProgressTime = CACurrentMediaTime()
            CATransaction.begin()
            CATransaction.setDisableActions(true)
            self.progressLayer.strokeEnd = 0.0
            CATransaction.commit()
        } else {
            // 常规：
            // - 首次初始化：对齐服务器，避免一进入就从0开始
            // - 非首次：仅更新服务器上限，不提升本地显示基线，避免"前跳"
            if !wasInitialized {
                self.displayedStepBaselineSeconds = serverStageSeconds
                self.lastDisplayedStepSeconds = serverStageSeconds
                self.globalTaskCurrentSeconds = serverStageSeconds
                self.globalTaskProgress = Double(serverStageSeconds) / Double(max(1, self.globalTaskTotalSeconds))
                // 首次初始化时立即更新UI显示
                CATransaction.begin()
                CATransaction.setDisableActions(true)
                self.progressLayer.strokeEnd = CGFloat(self.globalTaskProgress)
                CATransaction.commit()
            } else {
                self.globalTaskCurrentSeconds = serverStageSeconds
            }
            updateProgressDisplay()
        }

        // 不再启动本地计时器
    }

    // 移除旧的阶段单位推断逻辑；阶段长度直接由（下一阈值-上一阈值）决定

    // 本地累计接口已移除

    /// 更新任务进度显示
    private func updateTaskProgressDisplay() {
        // 直接调用统一的进度更新方法
        updateProgressDisplay()

        // 如果进度达到100%，可以添加完成动画
        if globalTaskProgress >= 1.0 {
            animateCompletion()
        }
    }

    /// 播放完成动画
    private func animateCompletion() {
        // 进度条闪烁效果
        let flashAnimation = CABasicAnimation(keyPath: "opacity")
        flashAnimation.fromValue = 1.0
        flashAnimation.toValue = 0.3
        flashAnimation.duration = 0.3
        flashAnimation.autoreverses = true
        flashAnimation.repeatCount = 3

        progressLayer.add(flashAnimation, forKey: "completionFlash")
    }

    /// 获取当前全局任务信息
    func getCurrentGlobalTaskInfo() -> (progress: Double, serverSeconds: Int, localSeconds: Int, totalSeconds: Int, hasConfig: Bool) {
        // 本地累计已移除，localSeconds 固定为0
        return (globalTaskProgress, globalTaskCurrentSeconds, 0, globalTaskTotalSeconds, hasGlobalTaskConfig)
    }

    /// 获取当前全局观看时间（兼容旧接口）
    func getCurrentGlobalWatchTime() -> TimeInterval {
        return TimeInterval(globalTaskCurrentSeconds)
    }

    /// 获取当前播放状态
    var isCurrentlyPlaying: Bool {
        return isPlaying
    }

    /// 可选：关闭“归零后填充”的阶段切换过渡，只保留前进效果
    func setStageResetFillEnabled(_ enabled: Bool) {
        resetFillOnNewStage = enabled
    }

    /// 设置视频已完整观看状态（停止计时但保持显示）
    func setVideoCompleted() {
        isPlaying = false
//        stopGoldCoinTimer()
        stopDisplayLink()
        // silent
    }

    // 阶段单位由任务配置驱动；当进入下一阶段时，通过再次调用
    // updateGlobalTaskConfig(totalSeconds: currentSeconds:) 以新的配置更新。
}

// MARK: - CAAnimationDelegate
extension GoldCoinView: CAAnimationDelegate {
    func animationDidStop(_ anim: CAAnimation, finished flag: Bool) {
        if flag {
            isAnimating = false
            if !isLoggedIn {
                // 未登录用户：动画完成后保持进度条填满状态
                progressLayer.strokeEnd = 1
            }
        }
    }
}

// MARK: - DisplayLink for smooth UI-only progress
private extension GoldCoinView {
    func startDisplayLinkIfNeeded() {
        guard displayLink == nil else { return }
        let link = CADisplayLink(target: self, selector: #selector(handleDisplayLink))
        link.add(to: .main, forMode: .common)
        displayLink = link
    }

    func stopDisplayLink() {
        displayLink?.invalidate()
        displayLink = nil
    }

    @objc func handleDisplayLink() {
        guard isPlaying, hasGlobalTaskConfig else { return }

        // 限制更新频率，避免过于频繁的UI更新
        let now = CACurrentMediaTime()
        if now - lastServerProgressTime < 0.1 { // 至少间隔0.1秒才更新
            return
        }

        // 持续按插值更新进度展示
        updateProgressDisplay()
    }
}
