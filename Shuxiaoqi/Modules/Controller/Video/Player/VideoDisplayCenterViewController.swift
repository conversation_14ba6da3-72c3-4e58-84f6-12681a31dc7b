//
//  VideoDisplayCenterViewController.swift
//  Shu<PERSON><PERSON>qi
//
//  Created by yong<PERSON>ng ye on 2025/3/26.
//

//  视频展示页

import UIKit
import Foundation
import SnapKit
import TXLiteAVSDK_UGC
import CoreImage

/**
 `VideoDisplayCenterViewController`
 
 播放器模块的核心容器 VC，负责纵向翻页播放短视频，功能涵盖：
 1. **数据源管理**：通过 `APIManager` 分页拉取视频流，支持外部指定 `videoItem` 作为起始视频。
 2. **页面复用/缓存**：结合 `UIPageViewController` 实现的页面滑动，维护 `videoPages` 数组；通过 `cleanupPages()` 保持前后各 2 页内存占用。
 3. **播放器生命周期**：在页面切换、VC 出现/消失、deinit 等时机统一暂停/释放 `TXVodPlayer`，避免资源泄漏。
 4. **交互入口**：自定义导航栏（返回 / 搜索 / 更多）与 `VideoPageDelegate` 分享回调。
 5. **嵌入灵活**：通过外部参数 `hideNavBackButton`、`showCustomNavBar`、`needsTabBarOffset` 适配不同父级场景（如 Tab 页、嵌套列表等）。
 
 资源管理策略：
 - **短视频常见滑动释放策略**：当滑动到第 N 页时，立即释放 N-2 之前的页面播放器，保持最多 5 个页面驻留。
 - **初始化保护**：`initializePlayerIfNeeded()` 防止重复创建播放器，保证每个页面仅 1 个 `TXVodPlayer` 实例。
 - **出错降级**：播放参数缺失时回退到错误占位视图，页面仍可正常滑动。
 
 日志打印已加入关键信息，可通过 Xcode Console 快速排查页面重复创建或资源未释放问题。
 */
class VideoDisplayCenterViewController: UIViewController {

    // MARK: - Types
    enum VideoListLoadType {
        case recommend  // 推荐流
        case follow     // 关注流
        case friend     // 朋友流
        case city       // 同城流
    }

    // MARK: - Properties
    private var currentIndex = 0
    private let pageSize = 3 // 预加载数量
    private var videoPages: [VideoPage] = []
    private var videoList: [VideoItem] = [] // 存储视频流列表
    // 外部可直接注入的完整视频列表（用于我的作品、喜欢、收藏等固定池）
    private var providedVideoList: [VideoItem]? = nil
    // 外部指定的初始索引，默认0
    private var initialIndex: Int = 0
    private var isLoadingMore = false // 是否正在加载更多
    private var currentPage = 0 // 当前页码
    private var hasMoreData = true // 是否还有更多数据
    private let loadSize = 10 // 每次加载的数量
    private var hasInitiallyLoaded = false // 是否已经完成了首次加载

    // 视频列表类型，用于根据不同入口请求不同的列表数据
    // -1: 单视频模式（不加载列表，用于个人作品、收藏、点赞等）
    // -2: 草稿预览模式（不加载列表，禁用交互，用于草稿预览）
    // -3: 内容管理预览模式（不加载列表，禁用交互，用于内容管理中非已发布作品预览）
    // 0: 推荐流, 1: 关注流, 3: 朋友流, 4: 同城流
    public var videoListType: Int = 0

    // 同城相关属性
    public var areaCode: String?
    
    // 是否隐藏返回按钮，外部可配置
    public var hideNavBackButton: Bool = false
    // 是否需要为自定义 TabBar 预留底部间距 (58)
    public var needsTabBarOffset: Bool = true
    // 是否展示自定义导航栏，外部可配置 (默认为显示)
    public var showCustomNavBar: Bool = true
    
    // UI组件
    private let ui = VideoDisplayCenterUIComponents()

    // 金币控件
    private let goldCoinView = GoldCoinView()

    // 是否已经设置过金币控件（避免重复设置）
    private var hasSetupGoldCoin = false

    // 阶段化已接管奖励后的清零逻辑，不再依赖强制从0显示

    // 侧滑返回手势相关
    private var panGestureRecognizer: UIPanGestureRecognizer?
    private var interactiveTransition: UIPercentDrivenInteractiveTransition?

    // 朋友页面占位视图
    private var friendEmptyPlaceholderView: FriendEmptyPlaceholderView?

    // MARK: - 数据模型
    /// 若由首页推荐或搜索点击单个视频进入，则可传入完整的视频数据
    public var videoItem: VideoItem?
    
    /// 便利初始化：直接传入 VideoItem（推荐流模式，会拼接推荐视频）
    convenience init(videoItem: VideoItem,
                     hideNavBackButton: Bool = false,
                     showCustomNavBar: Bool = true,
                     needsTabBarOffset: Bool = true) {
        self.init()
        self.videoItem = videoItem
        self.videoListType = 0 // 明确设置为推荐流
        self.hideNavBackButton = hideNavBackButton
        self.showCustomNavBar = showCustomNavBar
        self.needsTabBarOffset = needsTabBarOffset
    }

    /// 便利初始化：关注流模式（会拼接关注视频）
    convenience init(followVideoItem: VideoItem,
                     hideNavBackButton: Bool = false,
                     showCustomNavBar: Bool = true,
                     needsTabBarOffset: Bool = true) {
        self.init()
        self.videoItem = followVideoItem
        self.videoListType = 1 // 明确设置为关注流
        self.hideNavBackButton = hideNavBackButton
        self.showCustomNavBar = showCustomNavBar
        self.needsTabBarOffset = needsTabBarOffset
    }

    /// 便利初始化：朋友流模式（会拼接朋友视频）
    convenience init(friendVideoItem: VideoItem,
                     hideNavBackButton: Bool = false,
                     showCustomNavBar: Bool = true,
                     needsTabBarOffset: Bool = true) {
        self.init()
        self.videoItem = friendVideoItem
        self.videoListType = 3 // 明确设置为朋友流
        self.hideNavBackButton = hideNavBackButton
        self.showCustomNavBar = showCustomNavBar
        self.needsTabBarOffset = needsTabBarOffset
    }

    /// 便利初始化：单个视频播放（不加载列表，主要用于个人作品、收藏、点赞等）
    convenience init(singleVideoItem: VideoItem,
                     hideNavBackButton: Bool = false,
                     showCustomNavBar: Bool = true,
                     needsTabBarOffset: Bool = true) {
        self.init()
        self.videoItem = singleVideoItem
        self.videoListType = -1 // 使用-1表示单视频模式，不加载列表
        self.hideNavBackButton = hideNavBackButton
        self.showCustomNavBar = showCustomNavBar
        self.needsTabBarOffset = needsTabBarOffset
    }

    /// 便利初始化：草稿预览模式（不加载列表，禁用交互，用于草稿预览）
    convenience init(draftPreviewItem: VideoItem,
                     hideNavBackButton: Bool = false,
                     showCustomNavBar: Bool = true,
                     needsTabBarOffset: Bool = true) {
        self.init()
        self.videoItem = draftPreviewItem
        self.videoListType = -2 // 使用-2表示草稿预览模式，不加载列表且禁用交互
        self.hideNavBackButton = hideNavBackButton
        self.showCustomNavBar = showCustomNavBar
        self.needsTabBarOffset = needsTabBarOffset
    }

    /// 便利初始化：内容管理预览模式（不加载列表，禁用交互，用于内容管理中非已发布作品预览）
    convenience init(contentManagementPreviewItem: VideoItem,
                     hideNavBackButton: Bool = false,
                     showCustomNavBar: Bool = true,
                     needsTabBarOffset: Bool = true) {
        self.init()
        self.videoItem = contentManagementPreviewItem
        self.videoListType = -3 // 使用-3表示内容管理预览模式，不加载列表且禁用交互
        self.hideNavBackButton = hideNavBackButton
        self.showCustomNavBar = showCustomNavBar
        self.needsTabBarOffset = needsTabBarOffset
    }

    /// 便利初始化：直接传入完整视频集合 + 初始索引，内部不再拉流
    convenience init(videoList: [VideoItem],
                     startIndex: Int = 0,
                     hideNavBackButton: Bool = false,
                     showCustomNavBar: Bool = true,
                     needsTabBarOffset: Bool = true) {
        self.init()

        print("=== VideoDisplayCenterViewController 初始化 ===")
        print("接收到的视频列表数量: \(videoList.count)")
        print("接收到的起始索引: \(startIndex)")

        // 打印前几个视频信息
        for (index, item) in videoList.prefix(min(5, videoList.count)).enumerated() {
            let marker = index == startIndex ? "👉" : "  "
            print("\(marker) 视频[\(index)]: ID=\(item.id ?? -1), 标题=\(item.worksTitle ?? "无标题")")
        }
        print("==========================================")

        self.providedVideoList = videoList
        self.initialIndex = startIndex
        self.hideNavBackButton = hideNavBackButton
        self.showCustomNavBar = showCustomNavBar
        self.needsTabBarOffset = needsTabBarOffset
    }
    
    /// 便利初始化方法，允许在创建时直接传入类型及导航栏配置
    convenience init(
        videoListType: Int,
        hideNavBackButton: Bool = false,
        showCustomNavBar: Bool = true,
        needsTabBarOffset: Bool = true
    ) {
        self.init()
        self.videoListType = videoListType
        self.hideNavBackButton = hideNavBackButton
        self.showCustomNavBar = showCustomNavBar
        self.needsTabBarOffset = needsTabBarOffset
    }

    /// 便利初始化：同城视频播放，传入当前视频item和同城标识
    convenience init(
        videoItem: VideoItem,
        areaCode: String,
        hideNavBackButton: Bool = false,
        showCustomNavBar: Bool = true,
        needsTabBarOffset: Bool = true
    ) {
        self.init()
        self.videoItem = videoItem
        self.videoListType = 4 // 同城流
        self.areaCode = areaCode
        self.hideNavBackButton = hideNavBackButton
        self.showCustomNavBar = showCustomNavBar
        self.needsTabBarOffset = needsTabBarOffset

        print("[VideoDisplay] 同城初始化：videoItem=\(videoItem.worksTitle ?? "未知"), areaCode=\(areaCode)")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        
        // Debug: 打印传入的视频数据，确保值正确
        if let item = videoItem {
            let vid = item.id != nil ? String(item.id!) : "nil"
            print("[VideoDisplay] Received VideoItem -> id: \(vid), title: \(item.worksTitle ?? "nil"), sign: \(item.playSign ?? "nil"), videoId: \(item.videoId ?? "nil")")
        }
        
        // 若外部已提供列表，则直接使用，不再请求推荐流
        if let list = providedVideoList, !list.isEmpty {
            print("=== VideoDisplayCenterViewController 设置视频列表 ===")
            print("设置前 videoList 数量: \(self.videoList.count)")
            print("providedVideoList 数量: \(list.count)")

            self.videoList = list

            print("设置后 videoList 数量: \(self.videoList.count)")
            // 打印前几个视频信息确认数据正确
            for (index, item) in self.videoList.prefix(min(5, self.videoList.count)).enumerated() {
                print("videoList[\(index)]: ID=\(item.id ?? -1), 标题=\(item.worksTitle ?? "无标题")")
            }
            print("============================================")

            self.hasMoreData = false // 关闭分页加载
            self.setupInitialPages()
        } else if videoListType == -1 {
            // 单视频模式：只播放传入的视频，不加载列表
            if let item = videoItem {
                self.videoList = [item]
                self.hasMoreData = false // 关闭分页加载
                self.setupInitialPages()
                print("[VideoDisplay] 单视频模式：只播放传入的视频，不加载更多")
            } else {
                print("[VideoDisplay] 单视频模式：没有传入的videoItem，创建空页面")
                self.setupInitialPages()
            }
        } else if videoListType == -2 {
            // 草稿预览模式：只播放传入的视频，不加载列表，禁用交互
            if let item = videoItem {
                self.videoList = [item]
                self.hasMoreData = false // 关闭分页加载
                self.setupInitialPages()
                print("[VideoDisplay] 草稿预览模式：只播放传入的视频，禁用交互")
            } else {
                print("[VideoDisplay] 草稿预览模式：没有传入的videoItem，创建空页面")
                self.setupInitialPages()
            }
        } else if videoListType == -3 {
            // 内容管理预览模式：只播放传入的视频，不加载列表，禁用交互
            if let item = videoItem {
                self.videoList = [item]
                self.hasMoreData = false // 关闭分页加载
                self.setupInitialPages()
                print("[VideoDisplay] 内容管理预览模式：只播放传入的视频，禁用交互")
            } else {
                print("[VideoDisplay] 内容管理预览模式：没有传入的videoItem，创建空页面")
                self.setupInitialPages()
            }
        } else if videoListType == 0 && videoItem != nil {
            // 推荐流且有传入的videoItem：先显示传入的视频，然后加载推荐流拼接
            self.setupInitialVideoItemAndLoadMore(listType: .recommend)
        } else if videoListType == 1 && videoItem != nil {
            // 关注流且有传入的videoItem：先显示传入的视频，然后加载关注流拼接
            self.setupInitialVideoItemAndLoadMore(listType: .follow)
        } else if videoListType == 3 && videoItem != nil {
            // 朋友流且有传入的videoItem：先显示传入的视频，然后加载朋友流拼接
            self.setupInitialVideoItemAndLoadMore(listType: .friend)
        } else if videoListType == 4 && videoItem != nil {
            // 同城视频播放：先将传入的视频添加到列表，然后加载更多同城视频
            self.setupInitialVideoItemAndLoadMore(listType: .city)
        } else {
            // 其他情况：直接初始化视频流（关注流、朋友流等）
            fetchVideoStream()
        }
        
        // 隐藏系统导航栏
        navigationController?.setNavigationBarHidden(true, animated: false)

        // 设置侧滑返回手势
        setupSwipeBackGesture()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.setNavigationBarHidden(true, animated: false)

        // 只在首次出现时设置金币状态，避免页面切换时重置
        if !hasSetupGoldCoin {
            setupCurrentVideoGoldCoin()
            hasSetupGoldCoin = true
        }
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)

        // 页面消失时停止所有播放器和金币计时器
        for page in videoPages {
            if let videoPage = page as? VideoPage {
                videoPage.player?.pause()
                videoPage.isPlaying = false
            }
        }

        // 停止金币计时器
        goldCoinView.stopAll()

        // 停止单视频观看计时器
        GoldCoinVideoWatchManager.shared.stopWatching()

        // 当从视频页面返回时，确保目标页面的 TabBar 状态正确
        if isMovingFromParent || isBeingDismissed {
            // 检查返回的目标页面是否是二级页面（非 TabBar 根视图控制器）
            if let navController = navigationController,
               navController.viewControllers.count > 1,
               let targetVC = navController.viewControllers[navController.viewControllers.count - 2] as? BaseViewController,
               !targetVC.isTabBarRootViewController {

                // 如果返回到二级页面，确保 TabBar 隐藏
                if let tabBarController = tabBarController as? CustomTabBarController {
                    DispatchQueue.main.async {
                        tabBarController.hideTabBar(animated: false)
                        print("[VideoDisplay] 返回到二级页面，确保 TabBar 隐藏")
                    }
                }
            }
        }

        print("[VideoDisplay] 视频中心页面消失，暂停所有播放器")
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        
        // 如果是被移除或退出（而非临时被其他视图覆盖），则释放所有资源
        if isMovingFromParent || isBeingDismissed {
            // 释放所有页面的播放器资源
            for page in videoPages {
                if let videoPage = page as? VideoPage {
                    videoPage.stopAndReleasePlayer()
                }
            }
            print("[VideoDisplay] 视频中心页面退出，释放所有播放器资源")
        }
    }
    
    deinit {
        // 确保所有视频页面的播放器都被正确释放
        for page in videoPages {
            // 停止播放
            if let videoPage = page as? VideoPage {
                videoPage.stopAndReleasePlayer()
            }
        }
        videoPages.removeAll()
        print("[VideoDisplay] VideoDisplayCenterViewController 已释放，所有播放器资源已清理")
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        // 设置页面控制器代理
        ui.pageViewController.dataSource = self
        ui.pageViewController.delegate = self
        
        // 设置背景色为黑色
        view.backgroundColor = .black
        ui.pageViewController.view.backgroundColor = .black
        
        // 设置UI
        ui.setupUI(
            in: self,
            showCustomNavBar: showCustomNavBar,
            hideNavBackButton: hideNavBackButton,
            needsTabBarOffset: needsTabBarOffset,
            backAction: #selector(backButtonTapped),
            searchAction: #selector(searchButtonTapped),
            moreAction: #selector(moreButtonTapped)
        )

        // 设置金币控件
        setupGoldCoinView()
    }

    // MARK: - Gold Coin Setup
    private func setupGoldCoinView() {
        // 添加金币控件到视图
        view.addSubview(goldCoinView)

        // 设置约束：40*40，位置上面140pt，右边20pt
        goldCoinView.snp.makeConstraints { make in
            make.size.equalTo(40)
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top).offset(140)
            make.trailing.equalToSuperview().offset(-20)
        }

        // 设置点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(goldCoinTapped))
        goldCoinView.addGestureRecognizer(tapGesture)
        goldCoinView.isUserInteractionEnabled = true

        // 移除测试回调：奖励逻辑以服务器上报为准

        // 设置单视频观看管理器回调
        setupVideoWatchManager()

        print("[VideoDisplay] 金币控件已设置完成")
    }

    /// 设置视频观看管理器
    private func setupVideoWatchManager() {
        let manager = GoldCoinVideoWatchManager.shared

        // 进度更新回调：直接使用后端阶段值刷新金币UI
        manager.onProgressUpdate = { [weak self] progress, current, total in
            DispatchQueue.main.async {
                self?.goldCoinView.updateGlobalTaskConfig(totalSeconds: total, currentSeconds: current)
            }
        }

        // 奖励获得回调（来自服务器）
        manager.onRewardEarned = { [weak self] coins, message in
            DispatchQueue.main.async {
                // concise: manager已经有奖励日志
                // 重新加载配置以获取最新的总时长/完成态
                self?.loadTaskConfig()
            }
        }

        // 任务完成回调：进入新一轮动画
        manager.onTaskCompleted = { [weak self] message in
            DispatchQueue.main.async {
                // 如果是视频已完整观看过，停止所有计时
                if message.contains("已完整观看过") {
                    self?.goldCoinView.setVideoCompleted()
                } else {
                    // 加载配置以对齐新一轮任务的总时长/状态
                    self?.loadTaskConfig()
                }
            }
        }

        // 错误回调（仅用于调试）
        manager.onError = { error in
            print("[VideoDisplay] 视频观看管理器错误: \(error)")
        }

        // 加载任务配置
        loadTaskConfig()

        print("[VideoDisplay] 视频观看管理器已设置完成")
    }

    // 移除：阶段时长初始化完全以任务配置为准

    /// 加载任务配置
    private func loadTaskConfig() {
        APIManager.shared.getTaskWatchVideoRewardConfig { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if response.isSuccess, let config = response.data {
                        // 特殊处理：若全局视频任务已完成（如当日任务做完），后端可能返回 state=2 且 viewingSeconds=0
                        // 此时应将进度条拉满，并且不再上报或重置
                        if config.state == 2 {
                            // 全部完成：直接显示满进度
                            self?.goldCoinView.updateGlobalTaskConfig(totalSeconds: config.conditionValue,
                                                                      currentSeconds: config.conditionValue)
                        } else {
                            // 后端已计算好的阶段总长与当前值，直接使用
                            let total = config.conditionValue
                            let current = config.viewingSeconds ?? 0
                            self?.goldCoinView.updateGlobalTaskConfig(totalSeconds: total,
                                                                      currentSeconds: current)
                        }
                        // concise: init log is printed by GoldCoinVideoWatchManager
                    }
                case .failure(let error):
                    print("[VideoDisplay] 任务配置加载失败: \(error)")
                }
            }
        }
    }

    @objc private func goldCoinTapped() {
        print("[VideoDisplay] 金币控件被点击")

        if AuthManager.shared.isLoggedIn {
            // 已登录：显示金币相关界面
            showGoldCoinInfo()
        } else {
            // 未登录：跳转登录页面
            showLoginPage()
        }
    }

    private func showGoldCoinInfo() {
        print("[VideoDisplay] 显示金币信息页面（模态全屏）")
        let taskCenterVC = GoldCoinSystemTaskCenterViewController()
        taskCenterVC.modalPresentationStyle = .fullScreen
        // 直接从当前可见控制器模态展示，确保在可见窗口层级内
        self.present(taskCenterVC, animated: true)
    }

    private func showLoginPage() {
        print("[VideoDisplay] 跳转登录页面")
        // 使用AuthCoordinator统一登录流程
        AuthCoordinator.shared.startLogin(from: self) { success in
            if success {
                print("[VideoDisplay] 登录成功，重新设置金币控件")
                // 登录成功后重新设置当前视频的金币状态
                DispatchQueue.main.async {
                    self.setupCurrentVideoGoldCoin()
                }
            }
        }
    }

    // 移除测试HUD逻辑，仅保留服务器奖励逻辑

    /// 显示视频观看任务奖励HUD
    /// - Parameters:
    ///   - coins: 奖励金币数量
    ///   - message: 奖励消息
    private func showVideoWatchRewardHUD(coins: Int, message: String) {
        // 静默处理，不显示弹窗，只在控制台输出
        print("[VideoDisplay] 视频观看任务奖励: \(coins)金币, 消息: \(message)")

        // 如果需要显示HUD，可以使用以下代码：
        /*
        let alert = UIAlertController(title: "观看奖励", message: "\(message)\n获得 \(coins) 金币", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
        */
    }

    private func setupCurrentVideoGoldCoin() {
        let isLoggedIn = AuthManager.shared.isLoggedIn

        // 统一使用金币控件的setupGoldCoin方法
        goldCoinView.setupGoldCoin(isUserLoggedIn: isLoggedIn)

        print("[VideoDisplay] 设置金币控件 - 登录状态: \(isLoggedIn)")
    }

    /// 获取当前视频项
    private func getCurrentVideoItem() -> VideoItem? {
        // 优先使用当前页面的视频数据
        if let currentPage = getCurrentVideoPage() {
            return currentPage.videoItem
        }
        // 回退到实例变量
        return videoItem
    }

    /// 获取当前视频页面
    private func getCurrentVideoPage() -> VideoPage? {
        if currentIndex < videoPages.count {
            return videoPages[currentIndex] as? VideoPage
        }
        return nil
    }

    /// 判断是否为笔记类型视频
    /// - Parameter videoItem: 视频项
    /// - Returns: 是否为笔记类型
    private func isNoteTypeVideo(_ videoItem: VideoItem) -> Bool {
        // 根据worksType字段判断，具体值需要根据后端定义确定
        // 这里假设worksType为2表示笔记类型，需要根据实际情况调整
        return videoItem.worksType == 2
    }

    // MARK: - Public Gold Coin Methods

    /// 视频开始播放时调用
    public func goldCoinStartPlaying() {
        goldCoinView.startPlaying()
    }

    /// 视频暂停播放时调用
    public func goldCoinPausePlaying() {
        goldCoinView.pausePlaying()
    }

    /// 视频拖动进度条时调用
    public func goldCoinSeekTo(time: TimeInterval) {
        goldCoinView.seekTo(time: time)
    }

    /// 隐藏金币控件
    public func hideGoldCoin() {
        goldCoinView.isHidden = true
    }

    /// 显示金币控件
    public func showGoldCoin() {
        goldCoinView.isHidden = false
    }

    /// 设置金币控件是否显示进度条（用于首页等场景）
    public func setGoldCoinProgressVisible(_ visible: Bool) {
        goldCoinView.setProgressVisible(visible)
    }

    // MARK: - 测试方法已移除：金币仅以服务器奖励逻辑为准

    // MARK: - Video Stream
    private func fetchVideoStream() {
        // 如果正在加载或没有更多数据，则不再加载
        if isLoadingMore || !hasMoreData {
            print("[VideoDisplay] 跳过加载视频流：isLoadingMore=\(isLoadingMore), hasMoreData=\(hasMoreData)")
            return
        }

        // 根据视频类型决定loadNumber
        let loadNumber: Int
        if videoListType == 4 {
            // 同城流：后端修改了加载方案，固定加载10条
            loadNumber = 10
        } else if videoListType == 1 {
            // 关注流：固定加载10条（与推荐流保持一致）
            loadNumber = 10
        } else if videoListType == 3 {
            // 朋友流：固定加载10条
            loadNumber = 10
        } else {
            // 推荐流：去掉本地页码缓存计算，一直请求10条，请求不到=没有更多（全部由后端计算）
            loadNumber = 10
        }

        print("[VideoDisplay] 开始获取视频流，类型: \(videoListType), 当前列表数量: \(videoList.count), loadNumber: \(loadNumber)")
        isLoadingMore = true

        // 记录是否是首次加载（使用类属性而非局部变量，防止页码重置导致重复"首次加载"）
        let isInitialLoad = !hasInitiallyLoaded

        // 根据视频类型调用不同的接口
        switch videoListType {
        case 1: // 关注流
            // 获取最后一条视频的信息用于翻页
            let lastVideo = isInitialLoad ? nil : videoList.last
            let createTime = lastVideo?.createTime
            let fromId = lastVideo?.id

            APIManager.shared.getFollowWorksList(size: loadNumber, createTime: createTime, fromId: fromId) { [weak self] result in
                self?.handleVideoStreamResult(result, loadNumber: loadNumber, isInitialLoad: isInitialLoad)
            }
        case 3: // 朋友流
            // 获取最后一条视频的信息用于翻页
            let lastVideo = isInitialLoad ? nil : videoList.last
            let createTime = lastVideo?.createTime
            let fromId = lastVideo?.id

            APIManager.shared.getFriendWorksList(size: loadNumber, createTime: createTime, fromId: fromId) { [weak self] result in
                self?.handleVideoStreamResult(result, loadNumber: loadNumber, isInitialLoad: isInitialLoad)
            }
        case 4: // 同城流
            let areaCodeToUse = areaCode ?? ""
            if AuthManager.shared.isLoggedIn {
                APIManager.shared.getCityWorksList(loadNumber: loadNumber, areaCode: areaCodeToUse) { [weak self] result in
                    self?.handleVideoStreamResult(result, loadNumber: loadNumber, isInitialLoad: isInitialLoad)
                }
            } else {
                let deviceId = UIDevice.current.identifierForVendor?.uuidString ?? ""
                APIManager.shared.getMainCityWorksList(deviceId: deviceId, loadNumber: loadNumber, areaCode: areaCodeToUse) { [weak self] result in
                    self?.handleVideoStreamResult(result, loadNumber: loadNumber, isInitialLoad: isInitialLoad)
                }
            }
        default: // 推荐流
            APIManager.shared.getVideoStream(loadNumber: loadNumber) { [weak self] result in
                guard let self = self else { return }
                self.isLoadingMore = false

                switch result {
                case .success(let response):
                    // 根据实际API返回结构，data直接是数组
                    if let list = response.data, !list.isEmpty {
                        self.handleSuccessfulVideoLoad(list, loadNumber: loadNumber, isInitialLoad: isInitialLoad)
                    } else {
                        print("[VideoDisplay] 获取推荐视频流成功但列表为空")
                        self.hasMoreData = false
                        if isInitialLoad {
                            self.setupInitialPages()
                        }
                    }
                case .failure(let error):
                    print("[VideoDisplay] Failed to fetch video stream: \(error.localizedDescription)")
                    if isInitialLoad {
                        self.setupInitialPages()
                    }
                }
            }
        }
    }

    // MARK: - 视频特殊处理
    /// 设置初始视频并加载更多视频拼接（支持推荐流和同城流）
    private func setupInitialVideoItemAndLoadMore(listType: VideoListLoadType) {
        guard let item = videoItem else {
            let typeName = listType == .recommend ? "推荐" : "同城"
            print("[VideoDisplay] \(typeName)模式：没有传入的videoItem，回退到普通加载")
            fetchVideoStream()
            return
        }

        let typeName: String
        switch listType {
        case .recommend: typeName = "推荐"
        case .follow: typeName = "关注"
        case .friend: typeName = "朋友"
        case .city: typeName = "同城"
        }

        print("[VideoDisplay] \(typeName)模式：设置初始视频并加载更多\(typeName)视频")
        print("[VideoDisplay] \(typeName)模式：传入视频 - \(item.worksTitle ?? "未知标题"), ID: \(item.id ?? 0)")

        // 1. 先将传入的视频添加到列表
        videoList = [item]
        print("[VideoDisplay] \(typeName)模式：初始视频列表设置完成，count: \(videoList.count)")

        // 2. 设置初始页面（显示传入的视频）
        setupInitialPages()

        // 3. 立即加载更多视频拼接在后面
        switch listType {
        case .recommend:
            loadMoreRecommendVideos()
        case .follow:
            loadMoreFollowVideos()
        case .friend:
            loadMoreFriendVideos()
        case .city:
            loadMoreCityVideos()
        }
    }

    /// 加载更多推荐视频（专门用于拼接）
    private func loadMoreRecommendVideos() {
        // 如果正在加载，则不再加载
        if isLoadingMore {
            print("[VideoDisplay] 推荐模式：正在加载中，跳过")
            return
        }

        let loadNumber = 10 // 固定加载10条

        print("[VideoDisplay] 推荐模式：开始加载更多推荐视频")
        isLoadingMore = true

        APIManager.shared.getVideoStream(loadNumber: loadNumber) { [weak self] result in
            self?.handleRecommendVideoLoadResult(result, loadNumber: loadNumber)
        }
    }

    /// 加载更多关注视频（专门用于拼接）
    private func loadMoreFollowVideos() {
        // 如果正在加载，则不再加载
        if isLoadingMore {
            print("[VideoDisplay] 关注模式：正在加载中，跳过")
            return
        }

        let size = 10 // 固定加载10条

        // 获取最后一条视频的信息用于翻页
        let lastVideo = videoList.last
        let createTime = lastVideo?.createTime
        let fromId = lastVideo?.id

        print("[VideoDisplay] 关注模式：开始加载更多关注视频")
        isLoadingMore = true

        APIManager.shared.getFollowWorksList(size: size, createTime: createTime, fromId: fromId) { [weak self] result in
            self?.handleFollowVideoLoadResult(result, loadNumber: size)
        }
    }

    /// 加载更多朋友视频（专门用于拼接）
    private func loadMoreFriendVideos() {
        // 如果正在加载，则不再加载
        if isLoadingMore {
            print("[VideoDisplay] 朋友模式：正在加载中，跳过")
            return
        }

        let size = 10 // 固定加载10条

        // 获取最后一条视频的信息用于翻页
        let lastVideo = videoList.last
        let createTime = lastVideo?.createTime
        let fromId = lastVideo?.id

        print("[VideoDisplay] 朋友模式：开始加载更多朋友视频")
        isLoadingMore = true

        APIManager.shared.getFriendWorksList(size: size, createTime: createTime, fromId: fromId) { [weak self] result in
            self?.handleFriendVideoLoadResult(result, loadNumber: size)
        }
    }

    /// 加载更多同城视频（专门用于拼接）
    private func loadMoreCityVideos() {
        // 如果正在加载，则不再加载
        if isLoadingMore {
            print("[VideoDisplay] 同城模式：正在加载中，跳过")
            return
        }

        let loadNumber = 10 // 固定加载10条
        let areaCodeToUse = areaCode ?? ""

        print("[VideoDisplay] 同城模式：开始加载更多视频，areaCode: \(areaCodeToUse)")
        isLoadingMore = true

        if AuthManager.shared.isLoggedIn {
            APIManager.shared.getCityWorksList(loadNumber: loadNumber, areaCode: areaCodeToUse) { [weak self] result in
                self?.handleCityVideoLoadResult(result, loadNumber: loadNumber)
            }
        } else {
            let deviceId = UIDevice.current.identifierForVendor?.uuidString ?? ""
            APIManager.shared.getMainCityWorksList(deviceId: deviceId, loadNumber: loadNumber, areaCode: areaCodeToUse) { [weak self] result in
                self?.handleCityVideoLoadResult(result, loadNumber: loadNumber)
            }
        }
    }

    /// 处理朋友视频加载结果（专门用于拼接）
    private func handleFriendVideoLoadResult(_ result: Result<VideoArrayResponse, APIError>, loadNumber: Int) {
        DispatchQueue.main.async {
            self.isLoadingMore = false

            switch result {
            case .success(let response):
                guard let list = response.data, !list.isEmpty else {
                    print("[VideoDisplay] 朋友模式：API返回空列表，没有更多数据")
                    self.hasMoreData = false
                    self.hasInitiallyLoaded = true
                    // 如果朋友视频列表为空，显示朋友空白占位
                    if self.videoListType == 3 && self.videoList.count <= 1 {
                        self.showFriendEmptyPlaceholder()
                    }
                    return
                }

                print("[VideoDisplay] 朋友模式：API返回 \(list.count) 个视频")

                // 过滤掉与当前正在播放的视频相同的项目
                let currentVideoId = self.videoItem?.id
                let filteredList = list.filter { $0.id != currentVideoId }
                print("[VideoDisplay] 朋友模式：过滤掉当前视频后剩余 \(filteredList.count) 个视频")

                // 添加到现有列表，避免重复
                var addedCount = 0
                for item in filteredList {
                    if !self.videoList.contains(where: { $0.id == item.id }) {
                        self.videoList.append(item)
                        addedCount += 1
                        print("[VideoDisplay] 朋友模式：添加视频 - \(item.worksTitle ?? "未知标题"), ID: \(item.id ?? 0)")
                    }
                }

                print("[VideoDisplay] 朋友模式：实际添加了 \(addedCount) 个视频，总视频数: \(self.videoList.count)")

                // 根据返回数据判断是否还有更多
                self.hasMoreData = !list.isEmpty

                // 标记为已完成首次加载
                self.hasInitiallyLoaded = true

                // 如果有朋友视频数据，隐藏占位视图
                if self.videoListType == 3 && !self.videoList.isEmpty {
                    self.hideFriendEmptyPlaceholder()
                }

            case .failure(let error):
                print("[VideoDisplay] 朋友模式：网络错误 - \(error.localizedDescription)")
                self.hasInitiallyLoaded = true
                self.hasMoreData = false
                // 如果朋友视频加载失败且只有初始视频，显示朋友空白占位
                if self.videoListType == 3 && self.videoList.count <= 1 {
                    self.showFriendEmptyPlaceholder()
                }
            }
        }
    }

    /// 处理关注视频加载结果（专门用于拼接）
    private func handleFollowVideoLoadResult(_ result: Result<VideoArrayResponse, APIError>, loadNumber: Int) {
        DispatchQueue.main.async {
            self.isLoadingMore = false

            switch result {
            case .success(let response):
                guard let list = response.data, !list.isEmpty else {
                    print("[VideoDisplay] 关注模式：API返回空列表，没有更多数据")
                    self.hasMoreData = false
                    self.hasInitiallyLoaded = true
                    return
                }

                print("[VideoDisplay] 关注模式：API返回 \(list.count) 个视频")

                // 过滤掉与当前正在播放的视频相同的项目
                let currentVideoId = self.videoItem?.id
                let filteredList = list.filter { $0.id != currentVideoId }
                print("[VideoDisplay] 关注模式：过滤掉当前视频后剩余 \(filteredList.count) 个视频")

                // 添加到现有列表，避免重复
                var addedCount = 0
                for item in filteredList {
                    if !self.videoList.contains(where: { $0.id == item.id }) {
                        self.videoList.append(item)
                        addedCount += 1
                        print("[VideoDisplay] 关注模式：添加视频 - \(item.worksTitle ?? "未知标题"), ID: \(item.id ?? 0)")
                    }
                }

                print("[VideoDisplay] 关注模式：实际添加了 \(addedCount) 个视频，总视频数: \(self.videoList.count)")

                // 根据返回数据判断是否还有更多
                self.hasMoreData = !list.isEmpty

                // 标记为已完成首次加载
                self.hasInitiallyLoaded = true

            case .failure(let error):
                print("[VideoDisplay] 关注模式：网络错误 - \(error.localizedDescription)")
                self.hasInitiallyLoaded = true
                self.hasMoreData = false
            }
        }
    }

    /// 处理推荐视频加载结果（专门用于拼接）
    private func handleRecommendVideoLoadResult(_ result: Result<VideoArrayResponse, APIError>, loadNumber: Int) {
        DispatchQueue.main.async {
            self.isLoadingMore = false

            switch result {
            case .success(let response):
                guard let list = response.data, !list.isEmpty else {
                    print("[VideoDisplay] 推荐模式：API返回空列表，没有更多数据")
                    self.hasMoreData = false
                    self.hasInitiallyLoaded = true
                    return
                }

                print("[VideoDisplay] 推荐模式：API返回 \(list.count) 个视频")

                // 过滤掉与当前正在播放的视频相同的项目
                let currentVideoId = self.videoItem?.id
                let filteredList = list.filter { $0.id != currentVideoId }
                print("[VideoDisplay] 推荐模式：过滤掉当前视频后剩余 \(filteredList.count) 个视频")

                // 添加到现有列表，避免重复
                var addedCount = 0
                for item in filteredList {
                    if !self.videoList.contains(where: { $0.id == item.id }) {
                        self.videoList.append(item)
                        addedCount += 1
                        print("[VideoDisplay] 推荐模式：添加视频 - \(item.worksTitle ?? "未知标题"), ID: \(item.id ?? 0)")
                    }
                }

                print("[VideoDisplay] 推荐模式：实际添加了 \(addedCount) 个视频，总视频数: \(self.videoList.count)")

                // 根据返回数据判断是否还有更多
                self.hasMoreData = !list.isEmpty

                // 标记为已完成首次加载
                self.hasInitiallyLoaded = true

            case .failure(let error):
                print("[VideoDisplay] 推荐模式：网络错误 - \(error.localizedDescription)")
                self.hasInitiallyLoaded = true
                self.hasMoreData = false
            }
        }
    }

    /// 处理同城视频加载结果（专门用于拼接）
    private func handleCityVideoLoadResult(_ result: Result<VideoArrayResponse, APIError>, loadNumber: Int) {
        DispatchQueue.main.async {
            self.isLoadingMore = false

            switch result {
            case .success(let response):
                guard response.isSuccess, let list = response.data else {
                    print("[VideoDisplay] 同城模式：API失败 - \(response.displayMessage)")
                    // 即使加载失败，也要标记为已完成首次加载，避免影响播放
                    self.hasInitiallyLoaded = true
                    return
                }

                print("[VideoDisplay] 同城模式：API返回 \(list.count) 个视频")

                if !list.isEmpty {
                    print("[VideoDisplay] 同城模式：开始拼接视频到列表")

                    // 由于后台改成无限刷新（重复视频随机出），我们允许重复视频
                    // 只过滤掉与当前正在播放的视频完全相同的项目
                    let currentVideoId = self.videoItem?.id
                    let filteredList = list.filter { $0.id != currentVideoId }
                    print("[VideoDisplay] 同城模式：过滤掉当前视频后剩余 \(filteredList.count) 个视频")

                    // 直接拼接到现有列表，允许重复视频
                    var addedCount = 0
                    for item in filteredList {
                        self.videoList.append(item)
                        addedCount += 1
                        print("[VideoDisplay] 同城模式：添加视频 - \(item.worksTitle ?? "未知标题"), ID: \(item.id ?? 0)")
                    }

                    print("[VideoDisplay] 同城模式：实际添加了 \(addedCount) 个视频，总视频数: \(self.videoList.count)")

                    // 根据后端逻辑：返回空列表=没有更多数据，返回非空列表=可能还有更多数据
                    self.hasMoreData = true

                    // 如果添加了新视频，通知可以继续滑动
                    if addedCount > 0 {
                        print("[VideoDisplay] 同城模式：新增了 \(addedCount) 个视频，用户可以继续滑动")
                    }
                } else {
                    print("[VideoDisplay] 同城模式：加载的视频列表为空，没有更多数据")
                    self.hasMoreData = false
                }

                // 标记为已完成首次加载
                self.hasInitiallyLoaded = true

            case .failure(let error):
                print("[VideoDisplay] 同城模式：网络错误 - \(error.localizedDescription)")
                // 即使加载失败，也要标记为已完成首次加载，避免影响播放
                self.hasInitiallyLoaded = true
                self.hasMoreData = false
            }
        }
    }

    // MARK: - Video Stream Result Handling
    /// 统一处理关注流和同城流的返回结果
    private func handleVideoStreamResult(_ result: Result<VideoArrayResponse, APIError>, loadNumber: Int, isInitialLoad: Bool) {
        DispatchQueue.main.async {
            self.isLoadingMore = false

            switch result {
            case .success(let response):
                guard response.isSuccess, let list = response.data else {
                    print("[VideoDisplay] API失败 - \(response.displayMessage)")
                    if isInitialLoad {
                        self.setupInitialPages()
                    }
                    return
                }

                if !list.isEmpty {
                    self.handleSuccessfulVideoLoad(list, loadNumber: loadNumber, isInitialLoad: isInitialLoad)
                } else {
                    print("[VideoDisplay] 获取视频流成功但列表为空")
                    self.hasMoreData = false
                    if isInitialLoad {
                        self.setupInitialPages()
                    }
                }

            case .failure(let error):
                print("[VideoDisplay] 网络错误 - \(error.localizedDescription)")
                if isInitialLoad {
                    self.setupInitialPages()
                }
            }
        }
    }

    /// 处理成功加载的视频数据
    private func handleSuccessfulVideoLoad(_ list: [VideoItem], loadNumber: Int, isInitialLoad: Bool) {
        print("[VideoDisplay] 成功获取视频流，数量: \(list.count)")

        // 打印每个视频的isMyWorks参数值
        for (index, video) in list.enumerated() {
            let title = video.worksTitle ?? "未知标题"
            let videoId = video.id != nil ? String(video.id!) : "未知ID"
            let isMyWorksValue = video.isMyWorks ?? false
            print("[VideoDisplay] 视频[\(index)] - 标题: \(title), ID: \(videoId), isMyWorks: \(isMyWorksValue)")
        }

        // 合并视频列表
        self.processVideoList(list)

        // 更新是否有更多数据
        if videoListType == 4 || videoListType == 0 {
            // 同城流和推荐流：当返回的数据为空时，表示已经刷完了（全部由后端计算）
            self.hasMoreData = !list.isEmpty
        } else {
            // 关注流：根据返回数量判断
            self.hasMoreData = list.count >= loadNumber
        }

        // 如果是首次加载成功，标记为已完成首次加载
        if isInitialLoad {
            self.hasInitiallyLoaded = true
            print("[VideoDisplay] 首次加载视频流，设置初始页面")
            self.setupInitialPages()
        } else {
            print("[VideoDisplay] 加载更多视频流，不重新设置页面")
        }
    }

    private func processVideoList(_ newList: [VideoItem]) {
        // 如果有传入的videoItem，将其作为第一个元素
        if let item = videoItem {
            // 打印传入的videoItem的isMyWorks参数值
            let title = item.worksTitle ?? "未知标题"
            let videoId = item.id != nil ? String(item.id!) : "未知ID"
            let isMyWorksValue = item.isMyWorks ?? false
            print("[VideoDisplay] 传入的videoItem - 标题: \(title), ID: \(videoId), isMyWorks: \(isMyWorksValue)")

            // 检查是否已经存在相同ID的视频，避免重复
            if !videoList.contains(where: { $0.id == item.id }) {
                videoList.insert(item, at: 0)
                print("[VideoDisplay] 将传入的videoItem插入到列表开头")
            } else {
                print("[VideoDisplay] 传入的videoItem已存在于列表中，跳过插入")
            }
        }

        // 添加新获取的视频
        if videoListType == 4 {
            // 同城模式：允许重复视频（后台改成无限刷新）
            print("[VideoDisplay] 同城模式：直接添加 \(newList.count) 个视频，允许重复")
            for item in newList {
                videoList.append(item)
            }
        } else {
            // 其他模式：避免重复
            for item in newList {
                if !videoList.contains(where: { $0.id == item.id }) {
                    videoList.append(item)
                }
            }
        }

        // 如果是朋友页面且有视频数据，隐藏占位视图
        if videoListType == 3 && !videoList.isEmpty {
            hideFriendEmptyPlaceholder()
        }

        print("[VideoDisplay] Video list updated, total count: \(videoList.count)")
    }

    // 加载更多视频
    private func loadMoreVideosIfNeeded() {
        // 确保至少有一些视频才考虑加载更多，避免在初始化阶段重复加载
        guard videoList.count > 0 && hasInitiallyLoaded else {
            print("[VideoDisplay] 跳过加载更多：videoList.count=\(videoList.count), hasInitiallyLoaded=\(hasInitiallyLoaded)")
            return
        }

        // 如果当前索引接近列表末尾，加载更多视频
        // 确保至少有3个视频才考虑"接近末尾"的逻辑，避免在初始阶段频繁触发
        let triggerIndex = max(videoList.count - 3, 2)
        if currentIndex >= triggerIndex && hasMoreData && !isLoadingMore {
            print("[VideoDisplay] 当前索引 \(currentIndex) 接近列表末尾（触发点: \(triggerIndex)），加载更多视频")

            if videoListType == 4 {
                // 同城模式：使用专门的同城视频加载方法
                loadMoreCityVideos()
            } else if videoListType == 0 && videoItem != nil {
                // 推荐流拼接模式：使用专门的推荐视频加载方法
                loadMoreRecommendVideos()
            } else if videoListType == 1 && videoItem != nil {
                // 关注流拼接模式：使用专门的关注视频加载方法
                loadMoreFollowVideos()
            } else if videoListType == 3 && videoItem != nil {
                // 朋友流拼接模式：使用专门的朋友视频加载方法
                loadMoreFriendVideos()
            } else {
                // 其他模式：使用通用的视频流加载方法
                fetchVideoStream()
            }
        }
    }

    private func setupInitialPages() {
        print("[VideoDisplay] 开始设置初始页面")
        // 释放之前的所有页面资源
        for page in videoPages {
            if let videoPage = page as? VideoPage {
                videoPage.stopAndReleasePlayer()
                print("[VideoDisplay] 重新设置初始页面，释放页面 \(videoPage.pageIndex) 的资源")
            }
        }
        videoPages.removeAll()
        
        // 添加日志，帮助排查重复创建问题
        print("[VideoDisplay] 设置初始页面，当前视频列表数量: \(videoList.count), 是否有初始视频项: \(videoItem != nil)")
        
        // 如果视频列表为空但有传入的videoItem，直接创建一个页面
        if videoList.isEmpty && videoItem != nil {
            let page = VideoPage(index: 0, videoItem: videoItem)
            page.delegate = self
            // 设置预览模式状态
            page.setPreviewMode(videoListType == -2 || videoListType == -3)
            videoPages = [page]
            print("[VideoDisplay] 创建单个初始页面，索引: 0")
        } else if !videoList.isEmpty {
            // 创建初始页面 - 确保包含 initialIndex
            let startTime = Date()

            // 计算需要创建的页面范围，确保包含 initialIndex
            let targetIndex = min(initialIndex, videoList.count - 1)
            let startIndex = max(0, targetIndex - 1) // 从目标索引前1个开始
            let endIndex = min(startIndex + pageSize, videoList.count) // 创建 pageSize 个页面

            print("[VideoDisplay] 创建页面范围: \(startIndex)..<\(endIndex), 目标索引: \(targetIndex)")

            videoPages = (startIndex..<endIndex).map { index -> VideoPage in
                let page = VideoPage(index: index, videoItem: videoList[index])
                page.delegate = self
                // 设置预览模式状态
                page.setPreviewMode(videoListType == -2 || videoListType == -3)
                print("[VideoDisplay] 批量创建初始页面，索引: \(index)")
                return page
            }
            print("[VideoDisplay] 批量创建 \(videoPages.count) 个页面，耗时: \(Date().timeIntervalSince(startTime))秒")
        } else {
            // 没有视频数据，检查是否是朋友页面
            if videoListType == 3 {
                // 朋友页面显示占位视图
                showFriendEmptyPlaceholder()
                print("[VideoDisplay] 朋友页面无视频数据，显示占位视图")
                return
            } else {
                // 其他页面创建空页面
                let page = VideoPage(index: 0)
                page.delegate = self
                // 设置预览模式状态
                page.setPreviewMode(videoListType == -2 || videoListType == -3)
                videoPages = [page]
                print("[VideoDisplay] 创建空页面，索引: 0")
            }
        }
        
        // 设置初始页面
        if let firstPage = videoPages.first {
            ui.pageViewController.setViewControllers(
                [firstPage],
                direction: .forward,
                animated: false
            )
            print("[VideoDisplay] 设置初始控制器页面，索引: \(firstPage.pageIndex)")
        }
        
        // 设置当前索引：外部指定优先，否则默认0
        if let _ = providedVideoList {
            currentIndex = min(initialIndex, max(0, videoList.count - 1))

            // 查找对应 currentIndex 的页面
            if let targetPage = videoPages.first(where: { $0.pageIndex == currentIndex }) {
                ui.pageViewController.setViewControllers([targetPage], direction: .forward, animated: false)
                // 重要：更新当前的videoItem为对应索引的视频数据
                self.videoItem = targetPage.videoItem
                print("[VideoDisplay] 跳转到索引 \(currentIndex)，更新videoItem为: \(targetPage.videoItem?.worksTitle ?? "未知")")

                // 确保初始页面加载评论数量
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    targetPage.loadCommentCount()
                    print("[VideoDisplay] 为初始页面手动触发评论数量加载（延迟0.3秒）")
                }
            } else {
                // 如果没有找到对应的页面，使用第一个页面并更新 currentIndex
                if let firstPage = videoPages.first {
                    currentIndex = firstPage.pageIndex
                    self.videoItem = firstPage.videoItem
                    print("[VideoDisplay] 未找到目标页面，使用第一个页面，索引: \(currentIndex)，videoItem: \(firstPage.videoItem?.worksTitle ?? "未知")")

                    // 确保初始页面加载评论数量
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        firstPage.loadCommentCount()
                        print("[VideoDisplay] 为初始页面手动触发评论数量加载（延迟0.3秒）")
                    }
                }
            }
        } else {
            currentIndex = 0
            // 确保初始页面加载评论数量
            if let firstPage = videoPages.first {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    firstPage.loadCommentCount()
                    print("[VideoDisplay] 为初始页面手动触发评论数量加载（延迟0.3秒）")
                }
            }
        }
        print("[VideoDisplay] 初始页面设置完成，当前索引: \(currentIndex)")

        // 为初始显示的视频添加观看记录
        if currentIndex < videoPages.count {
            let currentPage = videoPages[currentIndex]
            if let videoPage = currentPage as? VideoPage {
                print("[VideoDisplay] 为初始显示的视频添加观看记录")
                addWatchRecord(for: videoPage.videoItem)
            }
        }
    }
    
    // MARK: - Page Management
    private func getPage(at index: Int) -> VideoPage? {
        return videoPages.first { $0.pageIndex == index }
    }
    
    private func createNewPage(at index: Int) -> VideoPage {
        // 如果该索引的页面已存在，则直接返回，避免创建重复页面
        if let existing = getPage(at: index) {
            return existing
        }
        // 检查索引是否在视频列表范围内
        let videoItem = index < videoList.count ? videoList[index] : nil

        let page = VideoPage(index: index, videoItem: videoItem)
        page.delegate = self
        // 设置预览模式状态
        page.setPreviewMode(videoListType == -2 || videoListType == -3)
        videoPages.append(page)

        // 打印调试信息
        if let item = videoItem {
            print("[VideoDisplay] Creating page at index \(index) with video: \(item.worksTitle ?? "未知标题"), ID: \(item.id ?? 0)")
        } else {
            print("[VideoDisplay] Creating page at index \(index) with nil videoItem (videoList.count: \(videoList.count))")
        }
        
        // 清理不需要的页面
        cleanupPages()
        
        return page
    }
    
    // 重置到顶部页面，释放所有其他页面资源
    private func resetToTopPage() {
        // 停止并释放所有页面的播放器资源
        for page in videoPages {
            if let videoPage = page as? VideoPage {
                videoPage.stopAndReleasePlayer()
            }
        }
        
        // 清空页面数组
        videoPages.removeAll()
        
        // 重新创建顶部页面
        if !videoList.isEmpty {
            let topPage = VideoPage(index: 0, videoItem: videoList.first)
            topPage.delegate = self
            // 设置预览模式状态
            topPage.setPreviewMode(videoListType == -2 || videoListType == -3)
            videoPages = [topPage]
            
            // 设置为当前页面
            ui.pageViewController.setViewControllers(
                [topPage],
                direction: .forward,
                animated: false
            )
            
            // 重置当前索引
            currentIndex = 0
            
            print("[VideoDisplay] 已重置到顶部页面，释放所有播放器资源")
        }
    }
    
    private func cleanupPages() {
        // 短视频应用资源管理机制：滑到第三个才释放第一个
        // 保持前后各两页的缓存，即当前页面的前两个和后两个
        let visibleIndices = (currentIndex - 2...currentIndex + 2)
        
        // 找出需要移除的页面
        let pagesToRemove = videoPages.filter { !visibleIndices.contains($0.pageIndex) }
        
        // 释放这些页面的播放器资源
        for page in pagesToRemove {
            if let videoPage = page as? VideoPage {
                videoPage.stopAndReleasePlayer()
                print("[VideoDisplay] 清理页面索引 \(videoPage.pageIndex) 的播放器资源")
            }
        }
        
        // 从数组中移除这些页面
        videoPages = videoPages.filter { visibleIndices.contains($0.pageIndex) }
    }
    
    // MARK: - Actions
    @objc private func backButtonTapped() {
        if let navigationController = navigationController {
            // 检查导航控制器是否只有一个视图控制器（即当前是根视图控制器）
            if navigationController.viewControllers.count == 1 {
                // 如果是根视图控制器，且导航控制器是模态弹出的，则dismiss整个导航控制器
                if navigationController.presentingViewController != nil {
                    navigationController.dismiss(animated: true)
                } else {
                    // 否则尝试pop
                    navigationController.popViewController(animated: true)
                }
            } else {
                // 如果不是根视图控制器，正常pop
                navigationController.popViewController(animated: true)
            }
        } else {
            dismiss(animated: true)
        }
    }
    
    @objc private func searchButtonTapped() {
        print("搜索按钮被点击")

        // 创建搜索页面
        let searchVC = SearchViewController()

        // 根据当前的展示方式选择不同的跳转方式
        if let navigationController = navigationController {
            // 如果有导航控制器，使用push方式
            navigationController.pushViewController(searchVC, animated: true)
        } else {
            // 如果是模态展示，创建新的导航控制器
            let navController = UINavigationController(rootViewController: searchVC)
            navController.modalPresentationStyle = .fullScreen
            present(navController, animated: true)
        }
    }
    
    // MARK: - Share Sheet
    @objc func showShareSheet() {
        // 获取当前播放的视频信息
        guard currentIndex < videoList.count else {
            print("[VideoShare] 当前索引超出视频列表范围")
            return
        }

        let currentVideo = videoList[currentIndex]

        // 构建视频标题，最多10个字，超出截取
        let videoTitle = currentVideo.worksTitle ?? "精彩视频"
        let truncatedTitle = String(videoTitle.prefix(10))
        let finalTitle = truncatedTitle.count < videoTitle.count ? truncatedTitle + "..." : truncatedTitle

        // 构建分享文案
        let shareText = "复制打开【树小柒】APP，看看《\(finalTitle)》"

        // 构建分享链接
        let videoId = String(currentVideo.id ?? 0)
        let shareLink = "https://yigou.gzyoushu.com/#/sxqVideo/videoDetail/videoDetail?id=\(String(describing: videoId))"

//        print("[VideoShare] 准备分享视频 - 标题: \(finalTitle), ID: \(videoId), 链接: \(shareLink)")

        let payload = SharePayload(
            title: shareText,
            description: shareText,
            link: shareLink,
            thumbnail: currentVideo.fullCoverImageURL,
            image: nil,
            type: .video(id: videoId, link: shareLink),
            extras: [
                "pageIndex": currentIndex,
                "videoItem": currentVideo,
                "shareText": shareText,
                "videoDisplayController": self // 添加对当前控制器的引用
            ]
        )
        NotificationCenter.default.post(name: .shareRequested, object: payload)
    }
    
    @objc private func moreButtonTapped() {
        showMoreActionSheet()
    }

    // MARK: - More Action Sheet
    private func showMoreActionSheet() {
        let actionSheet = VideoMoreActionSheet { [weak self] actionType in
            self?.handleMoreAction(actionType)
        }
        actionSheet.show(in: view)
    }

    private func handleMoreAction(_ actionType: VideoMoreActionType) {
        switch actionType {
        case .share:
            showShareSheet()
        case .report:
            handleReportAction()
        case .dislike:
            handleDislikeAction()
        }
    }

    private func handleReportAction() {
        // 获取当前视频信息
        guard currentIndex < videoList.count else {
            print("[VideoReport] 当前索引超出视频列表范围")
            return
        }

        let currentVideo = videoList[currentIndex]
        print("[VideoReport] 举报视频 - ID: \(currentVideo.id ?? -1), 标题: \(currentVideo.worksTitle ?? "无标题")")

        // 创建举报页面
        let reportVC = VideoReportViewController(
            videoId: String(currentVideo.id ?? 0),
            reportedUserId: currentVideo.customerId ?? "",
            reportedUserName: currentVideo.svUserMainVo?.customerName ?? "",
            reportedUserAvatar: currentVideo.svUserMainVo?.wxAvator ?? ""
        )

        // 跳转到举报页面
        reportVC.modalPresentationStyle = .fullScreen
        self.present(reportVC, animated: true)
    }

    private func handleDislikeAction() {
        // 获取当前视频信息
        guard currentIndex < videoList.count else {
            print("[VideoDislike] 当前索引超出视频列表范围")
            // 无接口：也给出反馈，避免用户无响应
            self.showToast("反馈成功，将为你优化推荐结果")
            return
        }

        let currentVideo = videoList[currentIndex]
        print("[VideoDislike] 不喜欢视频 - ID: \(currentVideo.id ?? -1), 标题: \(currentVideo.worksTitle ?? "无标题")")

        // 无接口：直接提示 HUD
        self.showToast("反馈成功，将为你优化推荐结果")
    }

    // MARK: - 侧滑返回手势
    private func setupSwipeBackGesture() {
        // 创建侧滑手势识别器
        panGestureRecognizer = UIPanGestureRecognizer(target: self, action: #selector(handlePanGesture(_:)))
        panGestureRecognizer?.delegate = self
        view.addGestureRecognizer(panGestureRecognizer!)

        print("[VideoDisplay] 侧滑返回手势已设置")
    }

    @objc private func handlePanGesture(_ gesture: UIPanGestureRecognizer) {
        let translation = gesture.translation(in: view)
        let velocity = gesture.velocity(in: view)
        let progress = max(0, min(1, translation.x / view.bounds.width))

        switch gesture.state {
        case .began:
            // 只有从左边缘开始的右滑手势才触发返回
            let startLocation = gesture.location(in: view)
            if startLocation.x < 50 && velocity.x > 0 { // 从左边缘50px内开始且向右滑动
                interactiveTransition = UIPercentDrivenInteractiveTransition()

                // 根据当前的展示方式选择不同的返回方式
                if let navigationController = navigationController {
                    navigationController.popViewController(animated: true)
                } else {
                    dismiss(animated: true)
                }
                print("[VideoDisplay] 开始侧滑返回手势")
            }

        case .changed:
            guard let transition = interactiveTransition else { return }
            let clampedProgress = max(0, min(1, progress))
            transition.update(clampedProgress)

        case .ended, .cancelled:
            guard let transition = interactiveTransition else { return }

            // 根据滑动距离和速度决定是否完成返回
            let shouldComplete = progress > 0.3 || velocity.x > 1000

            if shouldComplete {
                transition.finish()
                print("[VideoDisplay] 侧滑返回完成")
            } else {
                transition.cancel()
                print("[VideoDisplay] 侧滑返回取消")
            }

            interactiveTransition = nil

        default:
            break
        }
    }
}

// MARK: - UIGestureRecognizerDelegate
extension VideoDisplayCenterViewController: UIGestureRecognizerDelegate {
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldRecognizeSimultaneouslyWith otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        // 允许侧滑手势与页面滑动手势同时识别
        if gestureRecognizer == panGestureRecognizer {
            return true
        }
        return false
    }

    func gestureRecognizerShouldBegin(_ gestureRecognizer: UIGestureRecognizer) -> Bool {
        // 只有侧滑手势需要特殊处理
        if gestureRecognizer == panGestureRecognizer {
            guard let panGesture = gestureRecognizer as? UIPanGestureRecognizer else { return false }

            let velocity = panGesture.velocity(in: view)
            let location = panGesture.location(in: view)

            // 只有从左边缘开始的水平右滑手势才触发
            let isFromLeftEdge = location.x < 50
            let isHorizontalSwipe = abs(velocity.x) > abs(velocity.y)
            let isRightSwipe = velocity.x > 0

            return isFromLeftEdge && isHorizontalSwipe && isRightSwipe
        }

        return true
    }
}

// MARK: - VideoPageDelegate
extension VideoDisplayCenterViewController: VideoPageDelegate {
    // 子页面回调
    func videoPageDidTapShare(_ page: VideoPage) {
        showShareSheet()
    }

    func videoPageDidStartPlaying(_ page: VideoPage) {
        // 只有当前页面的播放事件才传递给金币系统
        if page.pageIndex == currentIndex {
            // 若为笔记类型作品，不启动金币计时与上报
            if let item = page.videoItem, isNoteTypeVideo(item) {
                print("[VideoDisplay] 当前为笔记作品，暂停金币计时且不启动上报")
                goldCoinView.pausePlaying()
                // 保险：确保上报管理器处于非观看状态
                GoldCoinVideoWatchManager.shared.pauseWatching()
                return
            }

            // 先启动上报管理器，再启动金币UI计时，保证状态一致
            handleVideoWatchStart(page)
            goldCoinView.startPlaying()
        }
    }

    func videoPageDidPausePlaying(_ page: VideoPage) {
        // 只有当前页面的暂停事件才传递给金币系统
        if page.pageIndex == currentIndex {
            // 同时控制金币控件和上报管理器
            goldCoinView.pausePlaying()
            handleVideoWatchPause()
        }
    }

    func videoPageDidSeekTo(_ page: VideoPage, time: TimeInterval) {
        // 只有当前页面的拖动事件才传递给金币系统
        if page.pageIndex == currentIndex {
            // 同时控制金币控件和上报管理器
            goldCoinView.seekTo(time: time)
            handleVideoWatchSeek(time)
        }
    }

    /// 播放器定时进度增量回调（绑定到当前页面且仅视频类型时才计入金币上报）
    func videoPage(_ page: VideoPage, didPlayProgressDelta delta: TimeInterval) {
        guard page.pageIndex == currentIndex else { return }
        guard let item = page.videoItem, !isNoteTypeVideo(item) else { return }
        guard delta > 0 else { return }

        GoldCoinVideoWatchManager.shared.recordPlayback(deltaSeconds: delta)
    }

    // MARK: - Video Watch Management

    /// 处理视频开始播放
    private func handleVideoWatchStart(_ page: VideoPage) {
        guard let videoItem = page.videoItem,
              let videoId = videoItem.id else {
            print("[VideoDisplay] 视频数据不完整，无法开始观看计时")
            return
        }

        let isNoteType = isNoteTypeVideo(videoItem)
        let videoIdString = String(videoId)

        // 笔记类型不启动观看管理器，保持全局任务配置不变
        if isNoteType {
            print("[VideoDisplay] 笔记类型作品，不启动观看管理器")
            return
        }

        let manager = GoldCoinVideoWatchManager.shared

        // 检查是否是同一个视频的恢复播放
        if manager.currentWatchingVideoId == videoIdString && !manager.isCurrentlyWatching {
            // 恢复观看同一个视频
            print("[VideoDisplay] 恢复视频观看计时: \(videoIdString)")
            manager.resumeWatching()
        } else {
            // 开始观看新视频
            print("[VideoDisplay] 开始视频观看计时: \(videoIdString), 是否笔记: \(isNoteType)")
            manager.startWatching(videoId: videoIdString, isNoteType: isNoteType)
        }
    }

    /// 处理视频暂停播放
    private func handleVideoWatchPause() {
        print("[VideoDisplay] 暂停视频观看计时")
        GoldCoinVideoWatchManager.shared.pauseWatching()
    }

    /// 处理视频拖拽
    private func handleVideoWatchSeek(_ time: TimeInterval) {
        print("[VideoDisplay] 视频拖拽，重新开始计时")
        GoldCoinVideoWatchManager.shared.seekTo(time: time)
    }

    /// 处理视频页面切换
    private func handleVideoPageSwitched(to currentPage: VideoPage, from previousPages: [UIViewController]) {
        // 页面切换时，先暂停金币控件，避免在目标页面类型判定前继续运行
        goldCoinView.pausePlaying()

        // 停止之前视频的观看计时
        GoldCoinVideoWatchManager.shared.stopWatching()

        // 重要：保持全局任务进度显示，不要重置金币控件
        // 全局任务进度应该跨视频保持

        // 切换到新视频后，不立刻启动上报，等待真正开始播放事件(videoPageDidStartPlaying)
        if let videoItem = currentPage.videoItem,
           let videoId = videoItem.id {
            let isNoteType = isNoteTypeVideo(videoItem)
            let videoIdString = String(videoId)
            print("[VideoDisplay] 页面切换，目标视频: \(videoIdString), 是否笔记: \(isNoteType)")

            if isNoteType {
                print("[VideoDisplay] 笔记类型，不启动观看管理器，保持金币控件暂停")
                goldCoinView.pausePlaying()
            } else {
                print("[VideoDisplay] 等待实际播放开始再启动观看管理器")
            }
        }

        // 确保金币控件保持显示全局任务进度
        // 不重新调用setupCurrentVideoGoldCoin，避免重置进度
        print("[VideoDisplay] 页面切换完成，保持全局任务进度显示，等待播放开始事件")
    }
}

// MARK: - UIPageViewControllerDataSource
extension VideoDisplayCenterViewController: UIPageViewControllerDataSource {
    func pageViewController(
        _ pageViewController: UIPageViewController,
        viewControllerBefore viewController: UIViewController
    ) -> UIViewController? {
        // 草稿预览模式和内容管理预览模式下禁用上下滚动
        if videoListType == -2 || videoListType == -3 {
            return nil
        }

        guard let currentPage = viewController as? VideoPage else { return nil }
        let previousIndex = currentPage.pageIndex - 1

        if previousIndex >= 0 {
            return getPage(at: previousIndex) ?? createNewPage(at: previousIndex)
        }
        return nil
    }
    
    func pageViewController(
        _ pageViewController: UIPageViewController,
        viewControllerAfter viewController: UIViewController
    ) -> UIViewController? {
        // 草稿预览模式和内容管理预览模式下禁用上下滚动
        if videoListType == -2 || videoListType == -3 {
            return nil
        }

        guard let currentPage = viewController as? VideoPage else { return nil }
        let nextIndex = currentPage.pageIndex + 1

        // 如果接近列表末尾，尝试加载更多
        loadMoreVideosIfNeeded()

        // 如果下一个索引超出视频列表范围
        if nextIndex >= videoList.count {
            // 如果没有更多数据，则返回nil
            if !hasMoreData {
                print("[VideoDisplay] 下一个索引 \(nextIndex) 超出范围且没有更多数据，返回nil")
                addBounceEffectIfNeeded()
                return nil
            }

            // 如果有更多数据但正在加载中，返回nil等待加载完成
            if isLoadingMore {
                print("[VideoDisplay] 下一个索引 \(nextIndex) 超出范围但正在加载更多数据，返回nil")
                return nil
            }

            // 如果有更多数据但还没开始加载，也返回nil，避免创建空页面
            print("[VideoDisplay] 下一个索引 \(nextIndex) 超出范围，等待数据加载")
            return nil
        }

        // 创建新页面（只有在有视频数据时才创建）
        return getPage(at: nextIndex) ?? createNewPage(at: nextIndex)
    }
    
    // 添加弹簧效果
    private func addBounceEffectIfNeeded() {
        // 使用弹簧动画模拟拖动效果
        let bounceAnimation = CAKeyframeAnimation(keyPath: "transform.translation.y")
        bounceAnimation.values = [0, -20, 0]
        bounceAnimation.keyTimes = [0, 0.5, 1.0]
        bounceAnimation.duration = 0.3
        bounceAnimation.timingFunctions = [
            CAMediaTimingFunction(name: .easeOut),
            CAMediaTimingFunction(name: .easeIn)
        ]
        
        ui.pageViewController.view.layer.add(bounceAnimation, forKey: "bounceAnimation")
    }
}

// MARK: - UIPageViewControllerDelegate
extension VideoDisplayCenterViewController: UIPageViewControllerDelegate {
    func pageViewController(
        _ pageViewController: UIPageViewController,
        didFinishAnimating finished: Bool,
        previousViewControllers: [UIViewController],
        transitionCompleted completed: Bool
    ) {
        if completed,
           let currentVC = pageViewController.viewControllers?.first as? VideoPage {
            // 获取之前的索引
            let previousIndex = currentIndex
            
            // 更新当前索引
            currentIndex = currentVC.pageIndex

            // 重要：更新当前的videoItem为切换后页面的视频数据
            self.videoItem = currentVC.videoItem
            print("[VideoDisplay] 页面切换完成，更新videoItem为: \(currentVC.videoItem?.worksTitle ?? "未知")")

            // 重置之前页面的评论数请求状态（防止竞态条件）
            for previousVC in previousViewControllers {
                if let previousPage = previousVC as? VideoPage {
                    previousPage.resetCommentRequestState()
                }
            }

            // 获取最新的评论数量
            currentVC.loadCommentCount()

            // 添加详细日志，帮助排查重复创建问题
            print("[VideoDisplay] 页面切换完成：从 \(previousIndex) 到 \(currentIndex)，当前页面数量: \(videoPages.count)")
            for (i, page) in videoPages.enumerated() {
                if let vp = page as? VideoPage {
                    print("[VideoDisplay] 页面[\(i)]: 索引 \(vp.pageIndex), 有播放器: \(vp.player != nil)")
                }
            }
            
            // 确保当前页面的视频播放
            if let player = currentVC.player {
                // 从头开始播放视频
                currentVC.restartPlayFromBeginning()
                print("[VideoDisplay] 当前页面 \(currentVC.pageIndex) 的视频从头开始播放")
                if let title = currentVC.videoItem?.worksTitle {
                    print("[VideoStatus] \(title) - 视频开始播放")
                }

                // 添加观看记录
                addWatchRecord(for: currentVC.videoItem)
            } else if currentVC.videoItem != nil {
                // 如果没有播放器但有视频数据，初始化播放器
                print("[VideoDisplay] 当前页面 \(currentVC.pageIndex) 没有播放器，尝试初始化")
                currentVC.initializePlayerIfNeeded()

                // 添加观看记录
                addWatchRecord(for: currentVC.videoItem)
            }
            
            // 处理之前的页面
            for previousVC in previousViewControllers {
                if let previousPage = previousVC as? VideoPage {
                    // 如果前一个页面不在可见范围内，则立即停止并释放其播放器
                    if previousPage.pageIndex != currentIndex &&
                       previousPage.pageIndex != currentIndex - 1 &&
                       previousPage.pageIndex != currentIndex + 1 {
                        previousPage.stopAndReleasePlayer()
                        print("[VideoDisplay] 页面切换完成，停止并释放页面 \(previousPage.pageIndex) 的播放器")
                        if let title = previousPage.videoItem?.worksTitle {
                            print("[VideoStatus] \(title) - 播放器已释放")
                        }
                    }
                }
            }
            
            // 清理不需要的页面
            cleanupPages()
            
            // 如果接近列表末尾，尝试加载更多
            loadMoreVideosIfNeeded()

            // 页面切换完成后，处理视频观看状态
            handleVideoPageSwitched(to: currentVC, from: previousViewControllers)
            print("[VideoDisplay] 页面切换完成，当前索引: \(currentIndex)")
        }
    }
    
    // 添加页面切换开始的处理
    func pageViewController(
        _ pageViewController: UIPageViewController,
        willTransitionTo pendingViewControllers: [UIViewController]
    ) {
        // 页面切换开始时，暂停当前页面的播放
        if let currentVC = pageViewController.viewControllers?.first as? VideoPage,
           let pendingVC = pendingViewControllers.first as? VideoPage {
            currentVC.player?.pause()
            print("[VideoDisplay] 页面切换开始，从 \(currentVC.pageIndex) 到 \(pendingVC.pageIndex)，暂停当前页面播放")
            if let title = currentVC.videoItem?.worksTitle {
                print("[VideoStatus] \(title) - 视频已暂停")
            }

            // 同步暂停金币控件与上报管理器，防止切换过程产生无效进度
            goldCoinView.pausePlaying()
            GoldCoinVideoWatchManager.shared.pauseWatching()
        }
    }
}

@objcMembers
class VideoPlayerDelegate: NSObject, TXVodPlayListener {
    weak var videoPage: VideoPage?

    // 缓冲状态跟踪
    private var isBuffering = false
    private var lastBufferProgress: Float = 0.0

    init(videoPage: VideoPage) {
        self.videoPage = videoPage
        super.init()
    }
    
    func onPlayEvent(_ player: TXVodPlayer!, event EvtID: Int32, withParam param: [AnyHashable : Any]!) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self, let videoPage = self.videoPage else { return }
            
            switch EvtID {
            case 2004: // 播放开始
                print("[VideoPage] 页面 \(videoPage.pageIndex) 播放开始")
                videoPage.isPlaying = true
                videoPage.ui.updatePauseButtonVisibility(isPlaying: true)
                // 通知代理播放开始
                videoPage.delegate?.videoPageDidStartPlaying(videoPage)

            case 2006: // 播放结束
                print("[VideoPage] 页面 \(videoPage.pageIndex) 播放结束")
                // 循环播放已在播放器配置中设置，无需额外处理

            case 2007: // 数据缓冲中
                self.isBuffering = true
                print("[VideoPage] 页面 \(videoPage.pageIndex) 开始缓冲")

            case 2014: // 视频缓冲结束
                self.isBuffering = false
                // 缓冲结束时，设置缓冲进度为100%
                self.lastBufferProgress = 1.0
                print("[VideoPage] 页面 \(videoPage.pageIndex) 缓冲结束，设置缓冲进度为100%")
                videoPage.ui.updateBufferProgress(1.0)

            case 2005: // 播放进度更新
                // 从事件参数中获取可播放时长信息
                if let playableDuration = param["EVT_PLAYABLE_DURATION"] as? NSNumber,
                   let totalDuration = param["EVT_PLAY_DURATION"] as? NSNumber {
                    let bufferProgress = totalDuration.floatValue > 0 ?
                        min(playableDuration.floatValue / totalDuration.floatValue, 1.0) : 0.0

                    // 只有当缓冲进度有显著变化时才更新UI
                    if abs(bufferProgress - self.lastBufferProgress) > 0.01 {
                        self.lastBufferProgress = bufferProgress
                        print("[VideoPage] 页面 \(videoPage.pageIndex) 缓冲进度更新: \(bufferProgress)")
                        videoPage.ui.updateBufferProgress(bufferProgress)
                    }
                }

            case -2301: // 网络断连，且经多次重连抢救无效
                print("[VideoPage] 页面 \(videoPage.pageIndex) 播放失败：网络断连")
                videoPage.showPlayError(message: "网络连接失败，请检查网络设置")

            case -2303: // 加载视频失败
                print("[VideoPage] 页面 \(videoPage.pageIndex) 播放失败：加载视频失败")
                videoPage.showPlayError(message: "视频加载失败，请稍后重试")

            case -2305: // 解码失败
                print("[VideoPage] 页面 \(videoPage.pageIndex) 播放失败：解码失败")
                videoPage.showPlayError(message: "视频解码失败，请稍后重试")

            default:
                break
            }
        }
    }
    
    func onNetStatus(_ player: TXVodPlayer!, withParam param: [AnyHashable : Any]!) {
        // 网络状态变化，可以在这里处理网络相关的UI更新
    }
}

// MARK: - Friend Empty Placeholder
extension VideoDisplayCenterViewController {

    /// 显示朋友页面占位视图
    private func showFriendEmptyPlaceholder() {
        // 隐藏页面控制器
        ui.pageViewController.view.isHidden = true

        // 移除旧的占位视图
        friendEmptyPlaceholderView?.removeFromSuperview()

        // 创建新的占位视图
        let placeholderView = FriendEmptyPlaceholderView()
        friendEmptyPlaceholderView = placeholderView

        // 设置回调
        placeholderView.onScanTapped = { [weak self] in
            self?.handleScanTapped()
        }

        placeholderView.onWechatTapped = { [weak self] in
            self?.handleWechatTapped()
        }

        // 添加到视图
        view.addSubview(placeholderView)
        placeholderView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

    /// 隐藏朋友页面占位视图
    private func hideFriendEmptyPlaceholder() {
        friendEmptyPlaceholderView?.removeFromSuperview()
        friendEmptyPlaceholderView = nil
        ui.pageViewController.view.isHidden = false
    }

    /// 处理扫一扫点击
    private func handleScanTapped() {
        print("朋友页面扫一扫按钮点击")
        let scanVC = QRScanViewController()
        navigationController?.pushViewController(scanVC, animated: true)
    }

    /// 处理微信好友点击
    private func handleWechatTapped() {
        print("=== 朋友页面微信分享按钮点击 ===")

        // 检查微信是否可用
        guard WXApi.isWXAppInstalled() else {
            print("❌ 微信未安装")
            showToast("请先安装微信")
            return
        }

        guard WXApi.isWXAppSupport() else {
            print("❌ 微信版本不支持")
            showToast("微信版本过低，请升级微信")
            return
        }

        // 获取当前用户信息
        APIManager.shared.getUserInfo { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let userInfo):
                    guard let data = userInfo.data,
                          !data.customerAccount.isEmpty,
                          !data.customerId.isEmpty else {
                        print("❌ 无法获取用户信息")
                        self?.showToast("用户信息加载失败，请重试")
                        return
                    }

                    let treeNumber = data.customerAccount
                    let userId = data.customerId

                    print("用户ID: \(userId)")
                    print("树小柒号: \(treeNumber)")

                    // 构建分享口令
                    let commandString = "复制到【树小柒】APP添加\(treeNumber)"
                    print("分享口令: \(commandString)")

                    // 生成二维码并分享
                    print("开始生成二维码...")
                    self?.generateUserQRCodeWithRemoteAvatar(
                        userId: userId,
                        avatarURL: data.wxAvator
                    ) { [weak self] (qrCodeImage: UIImage?) in
                        guard let qrCodeImage = qrCodeImage else {
                            print("❌ 二维码生成失败")
                            self?.showToast("二维码生成失败，请重试")
                            return
                        }

                        print("✅ 二维码生成成功，尺寸: \(qrCodeImage.size)")

                        let payload = SharePayload(
                            title: "树小柒 · 我的好友码",
                            description: commandString,
                            link: nil,
                            thumbnail: nil,
                            image: qrCodeImage,
                            type: .invite(code: treeNumber),
                            extras: ["command": commandString]
                        )

                        print("发送分享通知...")
                        NotificationCenter.default.post(name: .shareRequested, object: payload)
                        print("分享通知已发送")
                    }

                case .failure(let error):
                    print("❌ 获取用户信息失败: \(error.localizedDescription)")
                    self?.showToast("获取用户信息失败，请重试")
                }
            }
        }
    }

    // MARK: - QR Code Generation
    /// 异步加载头像并生成二维码
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - avatarURL: 头像URL
    ///   - completion: 完成回调，返回生成的二维码图片
    private func generateUserQRCodeWithRemoteAvatar(userId: String, avatarURL: String?, completion: @escaping (UIImage?) -> Void) {
        // 先生成无头像的二维码
        let qrCodeWithoutAvatar = generateUserQRCode(userId: userId)

        // 如果没有头像URL，直接返回无头像二维码
        guard let avatarURLString = avatarURL,
              !avatarURLString.isEmpty,
              let url = URL(string: avatarURLString) else {
            DispatchQueue.main.async {
                completion(qrCodeWithoutAvatar)
            }
            return
        }

        // 异步加载头像
        DispatchQueue.global().async {
            if let data = try? Data(contentsOf: url), let avatar = UIImage(data: data) {
                // 生成带头像的二维码
                let qrCodeWithAvatar = self.generateUserQRCode(userId: userId, avatar: avatar)
                DispatchQueue.main.async {
                    completion(qrCodeWithAvatar)
                }
            } else {
                // 头像加载失败，返回无头像二维码
                DispatchQueue.main.async {
                    completion(qrCodeWithoutAvatar)
                }
            }
        }
    }

    /// 生成用户分享二维码
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - avatar: 用户头像（可选）
    /// - Returns: 生成的二维码图片
    private func generateUserQRCode(userId: String, avatar: UIImage? = nil) -> UIImage? {
        // 构建二维码数据: {"content":"<customerId>", "type":1}
        let qrDict: [String: Any] = [
            "content": userId,
            "type": 1
        ]

        guard let jsonData = try? JSONSerialization.data(withJSONObject: qrDict, options: []),
              let qrData = String(data: jsonData, encoding: .utf8) else {
            print("生成二维码 JSON 字符串失败")
            return nil
        }

        return generateQRCode(from: qrData, avatar: avatar)
    }

    /// 生成二维码图片
    /// - Parameters:
    ///   - data: 二维码数据字符串
    ///   - avatar: 中心头像（可选）
    /// - Returns: 生成的二维码图片
    private func generateQRCode(from data: String, avatar: UIImage? = nil) -> UIImage? {
        guard let qrFilter = CIFilter(name: "CIQRCodeGenerator") else {
            print("无法创建二维码生成器")
            return nil
        }

        qrFilter.setValue(data.data(using: .utf8), forKey: "inputMessage")
        qrFilter.setValue("H", forKey: "inputCorrectionLevel") // 高容错率

        guard let ciImage = qrFilter.outputImage else {
            print("无法生成二维码 CIImage")
            return nil
        }

        // 放大 CIImage
        let transform = CGAffineTransform(scaleX: 10, y: 10)
        let scaledCIImage = ciImage.transformed(by: transform)

        // 将 CIImage 转换为 CGImage
        let context = CIContext()
        guard let cgImage = context.createCGImage(scaledCIImage, from: scaledCIImage.extent) else {
            print("无法创建 CGImage")
            return nil
        }
        let qrCodeImage = UIImage(cgImage: cgImage)

        // 如果没有头像，直接返回二维码
        guard let avatar = avatar else {
            return qrCodeImage
        }

        // 生成带头像的二维码
        return addAvatarToQRCode(qrCodeImage: qrCodeImage, avatar: avatar)
    }

    /// 在二维码中心添加头像
    /// - Parameters:
    ///   - qrCodeImage: 原始二维码图片
    ///   - avatar: 头像图片
    /// - Returns: 带头像的二维码图片
    private func addAvatarToQRCode(qrCodeImage: UIImage, avatar: UIImage) -> UIImage? {
        // 开始绘制带头像的二维码
        UIGraphicsBeginImageContextWithOptions(qrCodeImage.size, false, qrCodeImage.scale)
        defer { UIGraphicsEndImageContext() }

        qrCodeImage.draw(in: CGRect(origin: .zero, size: qrCodeImage.size))

        // 计算头像绘制区域（二维码中心约1/4区域）
        let avatarSize = CGSize(width: qrCodeImage.size.width * 0.25, height: qrCodeImage.size.height * 0.25)
        let avatarX = (qrCodeImage.size.width - avatarSize.width) / 2
        let avatarY = (qrCodeImage.size.height - avatarSize.height) / 2
        let avatarRect = CGRect(x: avatarX, y: avatarY, width: avatarSize.width, height: avatarSize.height)

        // 绘制白色背景（增加对比度）
        let backgroundRect = avatarRect.insetBy(dx: -4, dy: -4) // 白色边框比头像稍大
        let backgroundPath = UIBezierPath(roundedRect: backgroundRect, cornerRadius: 8)
        UIColor.white.setFill()
        backgroundPath.fill()

        // 绘制头像
        let avatarPath = UIBezierPath(roundedRect: avatarRect, cornerRadius: 8)
        avatarPath.addClip() // 裁剪绘制区域为圆角矩形
        avatar.draw(in: avatarRect)

        // 获取最终图像
        return UIGraphicsGetImageFromCurrentImageContext()
    }

    // MARK: - 观看记录
    /// 添加观看记录
    /// - Parameter videoItem: 视频数据
    private func addWatchRecord(for videoItem: VideoItem?) {
        guard let videoItem = videoItem,
              let worksId = videoItem.id else {
            print("[VideoDisplay] 添加观看记录失败：videoItem 或 worksId 为空")
            return
        }

        print("[VideoDisplay] 开始添加观看记录：worksId=\(worksId), title=\(videoItem.worksTitle ?? "未知")")

        APIManager.shared.addWorksWatch(worksId: worksId) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(_):
                    print("[VideoDisplay] 观看记录添加成功：worksId=\(worksId)")
                case .failure(let error):
                    print("[VideoDisplay] 观看记录添加失败：worksId=\(worksId), error=\(error.localizedDescription)")
                }
            }
        }
    }

    // MARK: - 分享数更新
    /// 乐观更新分享数（立即更新UI，提升用户体验）
    /// - Parameter videoItem: 要更新分享数的视频项
    func performOptimisticShareUpdate(for videoItem: VideoItem) {
        print("[VideoDisplay] 乐观更新视频分享数，视频ID: \(videoItem.id ?? -1)")

        // 查找对应的视频页面并更新分享数
        for page in videoPages {
            if let videoPage = page as? VideoPage,
               let pageVideoItem = videoPage.videoItem,
               pageVideoItem.id == videoItem.id {

                // 更新 VideoItem 的分享数
                var updatedVideoItem = pageVideoItem
                let currentShareCount = updatedVideoItem.shareNumber ?? 0
                updatedVideoItem.shareNumber = currentShareCount + 1

                // 更新页面的 VideoItem
                videoPage.videoItem = updatedVideoItem

                // 立即更新UI显示 - 只更新分享按钮的数量
                let newShareCount = updatedVideoItem.shareNumber ?? 0
                videoPage.ui.updateInteractionButtonCount(buttonIndex: 2, count: newShareCount)

                print("[VideoDisplay] 已乐观更新视频分享数：\(currentShareCount) -> \(currentShareCount + 1)")
                break
            }
        }
    }
}
