//
//  FunctionsCell.swift
//  Shu<PERSON><PERSON>qi
//
//  Created by yong<PERSON><PERSON> ye on 2025/4/30.
//

import UIKit

// MARK: - FunctionsCell
class FunctionsCell: UITableViewCell {
    
    // MARK: - Properties
    
    // 我的订单标题容器
    private let orderTitleContainer: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        return view
    }()
    
    // 我的订单标题
    private let orderTitle: UILabel = {
        let label = UILabel()
        label.text = "我的订单"
        label.font = .systemFont(ofSize: 16, weight: .medium)
        label.textColor = UIColor(hex: "#333333")
        return label
    }()
    
    // 订单箭头
    private let orderArrow: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "left_menu_right_arrow")
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    // 订单功能容器
    private let orderFunctionsContainer: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        return view
    }()
    
    // 常用功能标题
    private let commonTitle: UILabel = {
        let label = UILabel()
        label.text = "常用功能"
        label.font = .systemFont(ofSize: 16, weight: .medium)
        label.textColor = UIColor(hex: "#333333")
        return label
    }()
    
    // 常用功能容器
    private let commonFunctionsContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 12
        view.layer.masksToBounds = true
        return view
    }()
    
    // 订单功能项数组
    private let orderFunctions = [
        ("待付款", "pending_payment"),
        ("待发货", "pending_delivery"),
        ("待收货", "pending_receipt"),
        ("待评价", "pending_review"),
        ("售后", "after_sale")
    ]
    
    // 常用功能项数组
    private let commonFunctions = [
        ("点赞作品", "like_works"),
        ("我的收藏", "my_collection"),
        ("浏览记录", "browse_history"),
        ("收货地址", "shipping_address"),
        ("学生认证", "student_verify"),
        ("积分中心", "points_center"),
        ("分佣中心", "commission_center"),
        ("我的券包", "my_coupons"),
        ("创作中心", "creation_center"),
        // 将“评论历史”改为“带货中心”，图标暂不改（后续由你替换）
        ("带货中心", "icon_carrying_center")
    ]
    
    // 添加点击回调闭包
    var orderFunctionTapped: ((Int) -> Void)?
    var commonFunctionTapped: ((Int) -> Void)?
    // 新增：订单标题区域点击回调
    var orderHeaderTapped: (() -> Void)?
    
    // 覆盖订单标题区域的透明按钮
    private let orderHeaderButton: UIButton = {
        let button = UIButton(type: .custom)
        button.backgroundColor = .clear
        return button
    }()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func createFunctionItem(title: String, imageName: String, isOrder: Bool = false) -> UIView {
        let container = UIView()
        
        // 添加背景视图（仅订单功能项需要）
        if isOrder {
            let backgroundView = UIView()
            backgroundView.backgroundColor = UIColor(hex: "#EDEDED")
            backgroundView.layer.cornerRadius = 8  // 修改圆角为8pt
            container.addSubview(backgroundView)
            backgroundView.snp.makeConstraints { make in
                make.center.equalToSuperview()
                make.size.equalTo(68)
            }
        }
        
        let imageView = UIImageView()
        imageView.image = UIImage(named: imageName)
        imageView.contentMode = .scaleAspectFit
        
        let label = UILabel()
        label.text = title
        label.font = .systemFont(ofSize: 12)
        label.textColor = UIColor(hex: "#333333")
        label.textAlignment = .center
        
        container.addSubview(imageView)
        container.addSubview(label)
        
        imageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.size.equalTo(32)  // 修改图标大小为32*32
            if isOrder {
                make.centerY.equalToSuperview().offset(-10)  // 在背景中垂直居中偏上
            } else {
                make.top.equalToSuperview()
            }
        }
        
        label.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(8)
            make.centerX.equalToSuperview()
        }
        
        return container
    }
    
    private func setupOrderFunctions() {
        let itemWidth = UIScreen.main.bounds.width / 5
        
        for (index, (title, imageName)) in orderFunctions.enumerated() {
            let item = createFunctionItem(title: title, imageName: imageName, isOrder: true)
            orderFunctionsContainer.addSubview(item)
            
            // 添加点击手势
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(orderItemTapped(_:)))
            item.tag = index
            item.isUserInteractionEnabled = true
            item.addGestureRecognizer(tapGesture)
            
            item.snp.makeConstraints { make in
                make.left.equalTo(CGFloat(index) * itemWidth)
                make.top.bottom.equalToSuperview()
                make.width.equalTo(itemWidth)
            }
        }
    }
    
    private func setupCommonFunctions() {
        // Define desired spacing (edge padding and inter-item spacing)
        let spacing: CGFloat = 10.0 
        // Calculate the container's width (screen width - container's horizontal padding)
        let containerWidth = UIScreen.main.bounds.width - 12.0 - 12.0 // 24pt total padding for commonFunctionsContainer
        // Calculate the width for each item
        // Total spacing = 6 gaps (2 outer, 4 inner)
        let totalSpacing = 6.0 * spacing
        let itemWidth = (containerWidth - totalSpacing) / 5.0
        
        // Ensure calculated itemWidth is valid
        guard itemWidth > 0 else {
            print("Error: Calculated itemWidth is not positive in FunctionsCell.")
            return
        }
        
        let itemHeight: CGFloat = 70 // Keep height the same
        
        for (index, (title, imageName)) in commonFunctions.enumerated() {
            let row = index / 5
            let col = index % 5
            
            let item = createFunctionItem(title: title, imageName: imageName)
            commonFunctionsContainer.addSubview(item)
            
            // 添加点击手势
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(commonItemTapped(_:)))
            item.tag = index
            item.isUserInteractionEnabled = true
            item.addGestureRecognizer(tapGesture)
            
            // Calculate the left inset for the current item
            // Formula: (col + 1) * spacing + col * itemWidth
            let leftInset = (CGFloat(col) + 1.0) * spacing + CGFloat(col) * itemWidth
            
            item.snp.makeConstraints { make in
                // Use calculated leftInset relative to the container
                make.left.equalTo(leftInset)
                // Vertical position remains the same
                make.top.equalTo(16 + CGFloat(row) * itemHeight) 
                // Use calculated itemWidth
                make.width.equalTo(itemWidth)
                make.height.equalTo(itemHeight)
            }
        }
    }
    
    // 添加点击事件处理方法
    @objc private func orderItemTapped(_ gesture: UITapGestureRecognizer) {
        if let index = gesture.view?.tag {
            orderFunctionTapped?(index)
        }
    }
    
    @objc private func commonItemTapped(_ gesture: UITapGestureRecognizer) {
        if let index = gesture.view?.tag {
            commonFunctionTapped?(index)
        }
    }
    
    // 添加订单标题点击手势
    private func setupUI() {
        backgroundColor = UIColor(hex: "#F7F7F9")
        selectionStyle = .none
        
        // 添加订单标题容器
        contentView.addSubview(orderTitleContainer)
        orderTitleContainer.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(36)
        }
        
        // 添加订单标题和箭头
        orderTitleContainer.addSubview(orderTitle)
        orderTitleContainer.addSubview(orderArrow)

        // 在订单标题区域添加透明按钮，用于统一点击事件
        orderTitleContainer.addSubview(orderHeaderButton)
        orderHeaderButton.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        orderHeaderButton.addTarget(self, action: #selector(orderHeaderButtonTapped), for: .touchUpInside)
        
        orderTitle.snp.makeConstraints { make in
            make.left.equalTo(20)
//            make.centerY.equalToSuperview()
            make.top.equalToSuperview().offset(2)
            make.right.equalTo(orderArrow.snp.left).offset(-8)  // 固定文字和箭头的间距为8pt
        }
        
        orderArrow.snp.makeConstraints { make in
            make.centerY.equalTo(orderTitle)
            make.size.equalTo(8)
        }
        
        // 添加订单功能容器
        contentView.addSubview(orderFunctionsContainer)
        orderFunctionsContainer.snp.makeConstraints { make in
            make.top.equalTo(orderTitleContainer.snp.bottom)
            make.left.right.equalToSuperview()
            make.height.equalTo(80)
        }
        
        // 添加订单功能项
        setupOrderFunctions()
        
        // 添加常用功能标题
        contentView.addSubview(commonTitle)
        commonTitle.snp.makeConstraints { make in
            make.top.equalTo(orderFunctionsContainer.snp.bottom).offset(20)
            make.left.equalTo(20)
        }
        
        // 添加常用功能容器
        contentView.addSubview(commonFunctionsContainer)
        commonFunctionsContainer.snp.makeConstraints { make in
            make.top.equalTo(commonTitle.snp.bottom).offset(12)
            make.left.equalTo(12)  // 左边距12pt
            make.right.equalTo(-12)  // 右边距12pt
            make.height.equalTo(152)  // 固定高度152pt
            make.bottom.equalToSuperview()
        }
        
        // 添加常用功能项
        setupCommonFunctions()
        
        // 移除旧的点击手势，改用透明按钮处理点击事件
    }
    
    // 新增：透明按钮点击事件
    @objc private func orderHeaderButtonTapped() {
        orderHeaderTapped?()
    }
}
