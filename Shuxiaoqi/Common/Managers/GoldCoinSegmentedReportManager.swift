//
//  GoldCoinSegmentedReportManager.swift
//  Shu<PERSON><PERSON>qi
//
//  Created by Augment Agent on 2025/9/12.
//

import Foundation

/// 金币分段上报管理器
/// 基于服务器驱动的简化进度管理方案：
/// 1. 试探1秒获取有效时长
/// 2. 按2或3的倍数分段上报
/// 3. 每次上报后拉取配置更新UI
/// 4. 完全依赖服务器进度，避免本地计算误差
class GoldCoinSegmentedReportManager {
    
    // MARK: - Singleton
    static let shared = GoldCoinSegmentedReportManager()
    private init() {}
    
    // MARK: - Properties
    
    /// 当前正在观看的视频ID
    private var currentVideoId: String?
    
    /// 是否正在观看
    private var isWatching: Bool = false
    
    /// 是否正在上报
    private var isReporting: Bool = false
    
    /// 视频有效时长（从试探获取）
    private var videoValidDuration: Int = 0
    
    /// 分段间隔（秒）
    private var segmentInterval: Int = 2
    
    /// 已上报的分段数
    private var reportedSegments: Int = 0
    
    /// 总分段数
    private var totalSegments: Int = 0
    
    /// 定时器
    private var reportTimer: Timer?
    
    /// 是否已完成试探
    private var hasProbed: Bool = false
    
    /// 是否视频已完成
    private var isVideoCompleted: Bool = false
    
    /// 是否全局任务已完成
    private var isGlobalTaskCompleted: Bool = false
    
    // MARK: - Callbacks
    
    /// 进度更新回调 (进度百分比, 当前秒数, 总秒数)
    var onProgressUpdate: ((Double, Int, Int) -> Void)?
    
    /// 获得奖励回调 (奖励金币数量, 奖励信息)
    var onRewardEarned: ((Int, String) -> Void)?
    
    /// 任务完成回调 (完成消息)
    var onTaskCompleted: ((String) -> Void)?
    
    /// 错误回调 (错误信息)
    var onError: ((String) -> Void)?
    
    // MARK: - Public Methods
    
    /// 开始观看视频
    /// - Parameters:
    ///   - videoId: 视频ID
    ///   - isNoteType: 是否为笔记类型作品
    func startWatching(videoId: String, isNoteType: Bool = false) {
        print("[SegmentedReport] 开始观看视频: \(videoId), 笔记类型: \(isNoteType)")
        
        // 如果是笔记类型，只显示UI不进行实际计时
        if isNoteType {
            print("[SegmentedReport] 笔记类型，不进行计时上报")
            return
        }
        
        // 停止之前的观看
        if isWatching {
            stopWatching()
        }
        
        // 设置新的观看状态
        currentVideoId = videoId
        isWatching = true
        hasProbed = false
        isVideoCompleted = false
        reportedSegments = 0
        totalSegments = 0
        videoValidDuration = 0
        
        // 先加载全局任务配置
        loadGlobalTaskConfig { [weak self] in
            // 配置加载完成后进行试探
            self?.probeVideoValidDuration()
        }
    }
    
    /// 暂停观看
    func pauseWatching() {
        guard isWatching else { return }
        print("[SegmentedReport] 暂停观看")
        
        stopReportTimer()
    }
    
    /// 恢复观看
    func resumeWatching() {
        guard isWatching, hasProbed, !isVideoCompleted else { return }
        print("[SegmentedReport] 恢复观看")
        
        startReportTimer()
    }
    
    /// 停止观看
    func stopWatching() {
        guard isWatching else { return }
        print("[SegmentedReport] 停止观看")
        
        stopReportTimer()
        resetState()
    }
    
    /// 拖拽进度条
    func seekTo(time: TimeInterval) {
        guard isWatching else { return }
        print("[SegmentedReport] 拖拽进度条，重置计时")
        
        // 拖拽时重置定时器，避免计时异常
        stopReportTimer()
        if hasProbed && !isVideoCompleted {
            startReportTimer()
        }
    }
    
    // MARK: - Private Methods
    
    /// 加载全局任务配置
    private func loadGlobalTaskConfig(completion: @escaping () -> Void) {
        APIManager.shared.getTaskWatchVideoRewardConfig { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if response.isSuccess, let config = response.data {
                        // 检查全局任务状态
                        if config.state == 2 {
                            self?.isGlobalTaskCompleted = true
                            // 全局任务已完成，显示满进度
                            self?.onProgressUpdate?(1.0, config.conditionValue, config.conditionValue)
                            print("[SegmentedReport] 全局任务已完成")
                            return
                        } else {
                            self?.isGlobalTaskCompleted = false
                            // 更新当前进度
                            let total = config.conditionValue
                            let current = config.viewingSeconds ?? 0
                            let progress = total > 0 ? min(Double(current) / Double(total), 1.0) : 0
                            self?.onProgressUpdate?(progress, current, total)
                        }
                    }
                    completion()
                case .failure(let error):
                    print("[SegmentedReport] 加载全局配置失败: \(error)")
                    self?.onError?("获取任务配置失败")
                    completion()
                }
            }
        }
    }
    
    /// 试探视频有效时长
    private func probeVideoValidDuration() {
        guard let videoId = currentVideoId, !isGlobalTaskCompleted else { return }
        guard !isReporting else { return }
        
        print("[SegmentedReport] 开始试探视频有效时长")
        isReporting = true
        
        APIManager.shared.reportVideoWatchProgress(videoId: videoId, watchSeconds: 1) { [weak self] result in
            DispatchQueue.main.async {
                self?.isReporting = false
                self?.handleProbeResponse(result)
            }
        }
    }
    
    /// 处理试探响应
    private func handleProbeResponse(_ result: Result<GoldCoinWatchVideoResponse, APIError>) {
        switch result {
        case .success(let response):
            if response.status == 500 {
                // 视频已完整观看过
                print("[SegmentedReport] 视频已完整观看过")
                isVideoCompleted = true
                onTaskCompleted?("视频已完整观看过")
                return
            }
            
            if response.isSuccess, let data = response.data {
                // 获取视频有效时长
                getVideoValidDuration { [weak self] validDuration in
                    self?.setupSegmentedReporting(validDuration: validDuration)
                }
                
                // 更新全局配置
                refreshGlobalConfig()
            }
            
        case .failure(let error):
            print("[SegmentedReport] 试探失败: \(error)")
            onError?("试探失败")
        }
    }
    
    /// 获取视频有效时长
    private func getVideoValidDuration(completion: @escaping (Int) -> Void) {
        guard let videoId = currentVideoId else { 
            completion(0)
            return 
        }
        
        APIManager.shared.getVideoWatchProgress(videoId: videoId) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if response.isSuccess, let data = response.data {
                        let validDuration = max(0, data.totalDuration - data.currentProgress)
                        print("[SegmentedReport] 获取到有效时长: \(validDuration)秒")
                        completion(validDuration)
                    } else {
                        completion(0)
                    }
                case .failure:
                    completion(0)
                }
            }
        }
    }
    
    /// 设置分段上报
    private func setupSegmentedReporting(validDuration: Int) {
        guard validDuration > 0 else {
            print("[SegmentedReport] 有效时长为0，不进行分段上报")
            return
        }
        
        videoValidDuration = validDuration
        hasProbed = true
        
        // 计算分段策略
        calculateSegmentStrategy(validDuration: validDuration)
        
        print("[SegmentedReport] 分段策略: 总时长\(validDuration)秒, 分\(totalSegments)段, 每段\(segmentInterval)秒")
        
        // 开始定时上报
        startReportTimer()
    }
    
    /// 计算分段策略（优化为更平滑的间隔）
    private func calculateSegmentStrategy(validDuration: Int) {
        // 为了让动画更平滑，优先使用较小的分段间隔
        if validDuration <= 10 {
            // 短视频：1秒分段，最平滑
            segmentInterval = 1
            totalSegments = validDuration
        } else if validDuration <= 30 {
            // 中等视频：2秒分段
            segmentInterval = 2
            totalSegments = (validDuration + 1) / 2  // 向上取整
        } else if validDuration <= 60 {
            // 长视频：3秒分段
            segmentInterval = 3
            totalSegments = (validDuration + 2) / 3  // 向上取整
        } else {
            // 超长视频：4秒分段
            segmentInterval = 4
            totalSegments = (validDuration + 3) / 4  // 向上取整
        }

        // 确保至少有1段
        if totalSegments == 0 {
            segmentInterval = validDuration
            totalSegments = 1
        }
    }
    
    /// 开始定时上报
    private func startReportTimer() {
        stopReportTimer()
        
        guard totalSegments > 0, reportedSegments < totalSegments else { return }
        
        print("[SegmentedReport] 开始定时上报，间隔: \(segmentInterval)秒")
        
        reportTimer = Timer.scheduledTimer(withTimeInterval: TimeInterval(segmentInterval), repeats: true) { [weak self] _ in
            self?.performSegmentReport()
        }
    }
    
    /// 停止定时上报
    private func stopReportTimer() {
        reportTimer?.invalidate()
        reportTimer = nil
    }
    
    /// 执行分段上报
    private func performSegmentReport() {
        guard let videoId = currentVideoId else { return }
        guard !isReporting else { return }
        guard reportedSegments < totalSegments else {
            stopReportTimer()
            return
        }
        
        print("[SegmentedReport] 执行第\(reportedSegments + 1)段上报")
        isReporting = true
        reportedSegments += 1
        
        APIManager.shared.reportVideoWatchProgress(videoId: videoId, watchSeconds: segmentInterval) { [weak self] result in
            DispatchQueue.main.async {
                self?.isReporting = false
                self?.handleSegmentReportResponse(result)
            }
        }
    }
    
    /// 处理分段上报响应
    private func handleSegmentReportResponse(_ result: Result<GoldCoinWatchVideoResponse, APIError>) {
        switch result {
        case .success(let response):
            if response.isSuccess, let data = response.data {
                // 检查是否有奖励
                if !data.claimedRewards.isEmpty {
                    let totalReward = data.claimedRewards.reduce(0) { $0 + $1.rewardValue }
                    print("[SegmentedReport] 获得奖励: \(totalReward)金币")
                    onRewardEarned?(totalReward, data.message)
                }
                
                // 检查视频是否完成
                if data.videoCompleted {
                    print("[SegmentedReport] 视频观看完成")
                    isVideoCompleted = true
                    onTaskCompleted?(data.message)
                    stopReportTimer()
                }
                
                // 刷新全局配置
                refreshGlobalConfig()
            }
            
        case .failure(let error):
            print("[SegmentedReport] 分段上报失败: \(error)")
            onError?("上报失败")
        }
    }
    
    /// 刷新全局配置
    private func refreshGlobalConfig() {
        APIManager.shared.getTaskWatchVideoRewardConfig { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if response.isSuccess, let config = response.data {
                        let total = config.conditionValue
                        let current = config.viewingSeconds ?? 0
                        let progress = total > 0 ? min(Double(current) / Double(total), 1.0) : 0
                        
                        print("[SegmentedReport] 刷新全局配置: \(current)/\(total)")
                        self?.onProgressUpdate?(progress, current, total)
                        
                        // 检查是否全局任务完成
                        if config.state == 2 {
                            self?.isGlobalTaskCompleted = true
                            self?.stopReportTimer()
                        }
                    }
                case .failure(let error):
                    print("[SegmentedReport] 刷新配置失败: \(error)")
                }
            }
        }
    }
    
    /// 重置状态
    private func resetState() {
        currentVideoId = nil
        isWatching = false
        hasProbed = false
        isVideoCompleted = false
        reportedSegments = 0
        totalSegments = 0
        videoValidDuration = 0
        segmentInterval = 2
    }
    
    // MARK: - Public Getters
    
    /// 获取当前观看状态
    var isCurrentlyWatching: Bool {
        return isWatching
    }
    
    /// 获取当前视频ID
    var currentWatchingVideoId: String? {
        return currentVideoId
    }
    
    /// 获取调试信息
    func getDebugInfo() -> String {
        var info = "=== GoldCoinSegmentedReportManager 状态 ===\n"
        info += "当前视频ID: \(currentVideoId ?? "无")\n"
        info += "观看状态: \(isWatching ? "观看中" : "未观看")\n"
        info += "有效时长: \(videoValidDuration)秒\n"
        info += "分段策略: \(totalSegments)段 x \(segmentInterval)秒\n"
        info += "已上报: \(reportedSegments)/\(totalSegments)段\n"
        info += "视频完成: \(isVideoCompleted ? "是" : "否")\n"
        info += "全局完成: \(isGlobalTaskCompleted ? "是" : "否")\n"
        return info
    }
}
