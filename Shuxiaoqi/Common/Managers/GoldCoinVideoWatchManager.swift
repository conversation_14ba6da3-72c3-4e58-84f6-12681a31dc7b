//
//  GoldCoinVideoWatchManager.swift
//  Shuxiaoqi
//
//  Created by Augment Agent on 2025/9/5.
//

import Foundation

class GoldCoinVideoWatchManager {
    
    // MARK: - Singleton
    static let shared = GoldCoinVideoWatchManager()
    private init() {}
    
    // MARK: - Properties
    
    /// 当前正在观看的视频ID
    private var currentVideoId: String?
    
    /// 当前视频累计观看时长（仅调试用）
    private var accumulatedWatchTime: TimeInterval = 0

    /// 上次记录时间点
    private var lastRecordTime: Date?
    
    /// 是否正在观看
    private var isWatching: Bool = false
    
    /// 是否存在正在进行的上报
    private var isReporting: Bool = false
    /// 建议每3秒上报一次（播放器驱动的缓冲满足阈值后触发，不再使用固定Timer）
    private let reportIntervalSeconds: Int = 3

    /// 基于播放器回调累计的待上报整秒数（只累加整秒）
    private var pendingBufferSeconds: Int = 0
    /// 播放器回调产生的小数部分累加（<1.0）
    private var pendingFractionalSeconds: Double = 0.0

    /// 最近一次以服务端为准的阶段进度时间戳（用于估算边界时应上报的秒数）
    private var lastServerProgressUpdateAt: Date?

    /// 本次上报时，根据服务端配置计算出的“阶段剩余秒数”。用于在ACK成功后判断是否正好跨越阈值
    private var pendingReportedRemaining: Int?
    
    /// 当前视频的任务配置（全局水桶配置，后端已计算阶段值）
    private var currentVideoConfig: GoldCoinTaskWatchVideoRewardConfigData?
    
    /// 当前视频进度信息
    private var currentVideoProgress: GoldCoinVideoProgressData?
    
    /// 是否已完成当前视频任务
    private var isCurrentVideoCompleted: Bool = false

    /// 全局观看视频任务是否已完成（例如当日任务做完）
    private var isGlobalTaskCompleted: Bool = false

    /// 本次进入视频是否已进行过1秒探测上报
    private var didProbeOnStart: Bool = false

    // 不再需要本地基线计算，后端已返回阶段化后的总长度与当前进度
    
    // MARK: - Callbacks
    
    /// 进度更新回调 (进度百分比, 阶段内当前秒数, 阶段总秒数)
    /// 注意：直接使用后端返回的阶段化当前与总长度
    var onProgressUpdate: ((Double, Int, Int) -> Void)?
    
    /// 获得奖励回调 (奖励金币数量, 奖励信息)
    var onRewardEarned: ((Int, String) -> Void)?
    
    /// 任务完成回调 (完成消息)
    var onTaskCompleted: ((String) -> Void)?
    
    /// 错误回调 (错误信息) - 仅用于调试，不影响用户体验
    var onError: ((String) -> Void)?

    // 阶段时长以任务配置为准；不再单独回传下一阶段秒数
    
    // MARK: - Public Methods
    
    /// 开始观看视频
    /// - Parameters:
    ///   - videoId: 视频ID
    ///   - isNoteType: 是否为笔记类型作品
    func startWatching(videoId: String, isNoteType: Bool = false) {
        print("[GoldWatch] start videoId=\(videoId) note=\(isNoteType)")
        
        // 如果是笔记类型，只显示UI不进行实际计时
        if isNoteType {
            print("[GoldWatch] note-type: no timing / no report")
            // 笔记类型不触发任何进度更新，保持全局任务配置不变
            return
        }

        // 如果全局任务已完成，则不再进行计时和上报，但仍允许UI保持满进度
        if isGlobalTaskCompleted {
            print("[GoldWatch] global-completed: skip timing & reporting")
            // 仍可按需拉一次配置用于UI对齐（但不启动定时器）
            loadVideoConfigAndProgress(skipVideoProgress: true)
            return
        }
        
        // 如果正在观看其他视频，先停止
        if isWatching && currentVideoId != videoId {
            stopWatching()
        }
        
        // 设置新的观看状态
        currentVideoId = videoId
        isWatching = true
        lastRecordTime = Date()
        accumulatedWatchTime = 0
        isCurrentVideoCompleted = false
        
        // 获取视频任务配置和当前进度（先初始化金币UI）
        loadVideoConfigAndProgress()

        // 清空待上报缓存
        pendingBufferSeconds = 0
        pendingFractionalSeconds = 0
        didProbeOnStart = false
    }
    
    /// 暂停观看
    func pauseWatching() {
        guard isWatching else { return }
        
        print("[GoldWatch] pause")
        
        // 更新累计时长（仅调试）
        updateAccumulatedTime()

        // 尝试上报余量，避免丢时长
        tryFlushReports(force: true)
        
        isWatching = false
    }
    
    /// 恢复观看
    func resumeWatching() {
        guard currentVideoId != nil, !isWatching, !isCurrentVideoCompleted else { return }
        
        print("[GoldWatch] resume")
        
        isWatching = true
        lastRecordTime = Date()
        lastServerProgressUpdateAt = Date()
        
        // 恢复后由播放器回调推进缓冲并触发上报
    }
    
    /// 停止观看（切换视频或退出页面时调用）
    func stopWatching() {
        guard isWatching else { return }
        
        print("[GoldWatch] stop")
        
        // 更新累计时长（仅调试）
        updateAccumulatedTime()

        // 尝试上报余量
        tryFlushReports(force: true)
        
        // 重置状态
        resetState()
    }
    
    /// 拖拽进度条（不计入有效时长）
    func seekTo(time: TimeInterval) {
        guard isWatching else { return }
        
        print("[GoldWatch] seek: reset local timing window")
        
        // 重新开始计时，不累加拖拽前的时间
        lastRecordTime = Date()
        lastServerProgressUpdateAt = Date()
        // 清理小数累加，避免seek后产生异常delta
        pendingFractionalSeconds = 0.0
    }
    
    // MARK: - Private Methods
    
    /// 加载视频配置和进度
    private func loadVideoConfigAndProgress(skipVideoProgress: Bool = false) {
        guard let videoId = currentVideoId else { return }
        
        // 获取任务配置
        APIManager.shared.getTaskWatchVideoRewardConfig { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if response.isSuccess {
                        self?.currentVideoConfig = response.data
                        let total = response.data?.conditionValue ?? 0
                        let current = response.data?.viewingSeconds ?? 0
                        let reward = response.data?.rewardValue ?? 0
                        print("[GoldInit] total=\(total)s current=\(current)s reward=\(reward)")

                        // 特殊处理：当全局视频任务已完成（如当日任务做完）
                        // 后端会返回 state=2 且 viewingSeconds=0，这时前端应显示满进度并停止上报
                        let state = response.data?.state ?? 0
                        if total > 0 {
                            if state == 2 {
                                self?.isGlobalTaskCompleted = true
                                // 完成：阶段满格
                                self?.onProgressUpdate?(1.0, total, total)
                            } else {
                                self?.isGlobalTaskCompleted = false
                                // 直接使用后端阶段值
                                let progress = min(Double(current) / Double(total), 1.0)
                                self?.onProgressUpdate?(progress, current, total)
                            }
                            // 记录一次服务端进度基准时间
                            self?.lastServerProgressUpdateAt = Date()

                            // 配置初始化完成后，再进行1秒探测，避免全局已完成状态下的无效探测
                            if self?.isGlobalTaskCompleted == false, self?.didProbeOnStart == false {
                                self?.didProbeOnStart = true
                                self?.probeWatchOneSecond()
                            }
                        }
                    }
                case .failure(let error):
                    print("[GoldInit] config error: \(error)")
                    self?.onError?("获取任务配置失败")
                }
            }
        }
        
        // 获取当前视频进度（若全局任务已完成，可跳过）
        if !isGlobalTaskCompleted && !skipVideoProgress {
            APIManager.shared.getVideoWatchProgress(videoId: videoId) { [weak self] result in
                DispatchQueue.main.async {
                    switch result {
                    case .success(let response):
                        // 检查status=500，表示视频已完整观看过
                        if response.status == 500 {
                            print("[GoldVideo] already-completed; stop timing")
                            self?.isCurrentVideoCompleted = true
                            self?.onTaskCompleted?("视频已完整观看过")
                            self?.stopWatching()
                            return
                        }

                        if response.isSuccess {
                            self?.currentVideoProgress = response.data
                            let currentProgress = TimeInterval(response.data?.currentProgress ?? 0)
                            self?.accumulatedWatchTime = currentProgress
                            self?.isCurrentVideoCompleted = response.data?.completed ?? false

                            // UI阶段进度由任务配置驱动，这里不再下发阶段时长

                            let cur = response.data?.currentProgress ?? 0
                            let tot = response.data?.totalDuration ?? 0
                            let rem = max(0, tot - cur)
                            print("[GoldVideo] total=\(tot)s current=\(cur)s remaining=\(rem)s completed=\(self?.isCurrentVideoCompleted == true)")

                            // 更新UI显示
                            self?.updateProgressUI()

                            // 如果已完成，不再计时
                            if self?.isCurrentVideoCompleted == true {
                                self?.onTaskCompleted?("视频观看任务已完成")
                                self?.stopWatching()
                            }
                        }
                    case .failure(let error):
                        print("[GoldVideo] progress error: \(error)")
                        self?.onError?("获取视频进度失败")
                    }
                }
            }
        }
    }
    
    // 不再使用固定间隔Timer，上报由播放器回调驱动的缓冲触发
    
    /// 更新累计观看时长
    private func updateAccumulatedTime() {
        guard let lastTime = lastRecordTime else { return }

        let now = Date()
        let deltaTime = now.timeIntervalSince(lastTime)

        // 只有在正常播放状态下才累加时间
        if isWatching && deltaTime > 0 && deltaTime < 10 { // 防止异常大的时间差
            accumulatedWatchTime += deltaTime
            print("[GoldCoinVideoWatch] 更新累计时长: +\(deltaTime)s, 总计: \(accumulatedWatchTime)s")

            // 实时更新UI进度
            updateProgressUI()
        }

        lastRecordTime = now
    }
    
    /// 根据缓冲与边界条件尝试触发上报
    private func reportProgress(secondsToReport: Int) {
        guard let videoId = currentVideoId else { return }
        guard secondsToReport > 0 else { return }
        isReporting = true
        print("[GoldReport] +\(secondsToReport)s")
        APIManager.shared.reportVideoWatchProgress(videoId: videoId, watchSeconds: secondsToReport) { [weak self] result in
            DispatchQueue.main.async {
                self?.handleReportResponse(result, reportedIncrement: secondsToReport)
                self?.isReporting = false
                // 上报完成后，如还有缓存，继续尝试上报（避免串行积压）
                self?.tryFlushReports(force: false)
            }
        }
    }

    /// 播放器进度回调：累计播放时间，按阈值触发上报
    func recordPlayback(deltaSeconds: TimeInterval) {
        guard isWatching, !isCurrentVideoCompleted, !isGlobalTaskCompleted else { return }

        // 优化：过滤异常小的增量，避免频繁触发
        guard deltaSeconds > 0.1 else { return }

        // 累加小数部分，转换为整秒缓冲
        pendingFractionalSeconds += max(0, deltaSeconds)
        if pendingFractionalSeconds >= 1.0 {
            let whole = Int(pendingFractionalSeconds)
            pendingBufferSeconds += whole
            pendingFractionalSeconds -= Double(whole)

            // 调试日志：记录缓冲累积情况
            print("[GoldWatch] 缓冲累积: +\(whole)s, 总缓冲: \(pendingBufferSeconds)s")
        }
        // 非强制时，仅在达到阈值或接近边界时触发
        tryFlushReports(force: false)
    }

    /// 尝试将缓冲的整秒上报
    private func tryFlushReports(force: Bool) {
        guard !isReporting else {
            print("[GoldWatch] 跳过上报：正在上报中")
            return
        }

        // 若已完成或没有缓冲，无需上报
        if isCurrentVideoCompleted || isGlobalTaskCompleted { return }

        let available = pendingBufferSeconds
        if available <= 0 && !force { return }

        // 计算全局阶段剩余与当前视频剩余
        let stageRemaining = computeStageRemainingSeconds()
        let videoRemaining = computeVideoRemainingSeconds()
        if stageRemaining <= 0 || videoRemaining <= 0 {
            print("[GoldWatch] 跳过上报：已达边界 stageRemaining=\(stageRemaining), videoRemaining=\(videoRemaining)")
            return
        }

        var toSend = 0
        if force {
            toSend = min(available, stageRemaining, videoRemaining)
            print("[GoldWatch] 强制上报: available=\(available), toSend=\(toSend)")
        } else {
            // 优化：更智能的上报策略，避免突然满了的问题
            if available >= reportIntervalSeconds {
                toSend = min(reportIntervalSeconds, available, stageRemaining, videoRemaining)
                print("[GoldWatch] 达到阈值上报: available=\(available), toSend=\(toSend)")
            } else if stageRemaining <= 2 && available >= stageRemaining {
                // 当接近阶段边界（剩余2秒内）时，立即上报避免突然跳满
                toSend = min(available, stageRemaining, videoRemaining)
                print("[GoldWatch] 接近边界上报: stageRemaining=\(stageRemaining), toSend=\(toSend)")
            } else {
                // 缓冲不足阈值，且未逼近边界，先不报
                return
            }
        }

        guard toSend > 0 else { return }
        pendingBufferSeconds -= toSend
        reportProgress(secondsToReport: toSend)
    }

    /// 计算阶段剩余秒数（基于配置 viewingSeconds/conditionValue）
    private func computeStageRemainingSeconds() -> Int {
        guard let cfg = currentVideoConfig, cfg.conditionValue > 0 else { return Int.max }
        let total = cfg.conditionValue
        let current = cfg.viewingSeconds ?? 0
        return max(0, total - current)
    }

    /// 计算当前视频剩余秒数（基于进度 currentProgress/totalDuration）
    private func computeVideoRemainingSeconds() -> Int {
        guard let p = currentVideoProgress else { return Int.max }
        return max(0, p.totalDuration - p.currentProgress)
    }

    /// 进入视频后立即探测上报1秒
    private func probeWatchOneSecond() {
        guard let videoId = currentVideoId, !isCurrentVideoCompleted, !isGlobalTaskCompleted else { return }
        // 避免并发上报
        if isReporting { return }
        isReporting = true
        APIManager.shared.reportVideoWatchProgress(videoId: videoId, watchSeconds: 1) { [weak self] result in
            DispatchQueue.main.async {
                self?.isReporting = false
                switch result {
                case .success(let response):
                    // 500：该视频已完整观看过
                    if response.status == 500 {
                        print("[GoldProbe] already-completed; skip")
                        self?.isCurrentVideoCompleted = true
                        self?.onTaskCompleted?(response.msg)
                        self?.pendingBufferSeconds = 0
                        self?.pendingFractionalSeconds = 0
                        return
                    }
                    if response.isSuccess, let data = response.data {
                        // 乐观推进阶段进度 +1（后续通过配置刷新对齐）
                        if var cfg = self?.currentVideoConfig {
                            let total = cfg.conditionValue
                            let cur = min(total, (cfg.viewingSeconds ?? 0) + 1)
                            cfg.viewingSeconds = cur
                            self?.currentVideoConfig = cfg
                            let progress = total > 0 ? min(Double(cur) / Double(total), 1.0) : 0
                            self?.onProgressUpdate?(progress, cur, total)
                        }
                        // 更新视频进度（若已拉取过）
                        if var p = self?.currentVideoProgress {
                            p.currentProgress = min(p.totalDuration, p.currentProgress + data.addedSeconds)
                            p.completed = data.videoCompleted
                            self?.currentVideoProgress = p
                        }
                        // 拉一次配置以对齐服务端阶段/状态
                        self?.fetchAndNotifyGlobalConfig()
                        // 拉取视频进度以获取最新剩余
                        if let vid = self?.currentVideoId {
                            APIManager.shared.getVideoWatchProgress(videoId: vid) { [weak self] result in
                                if case .success(let r) = result, r.isSuccess {
                                    self?.currentVideoProgress = r.data
                                }
                            }
                        }
                    }
                case .failure(let err):
                    print("[GoldProbe] fail: \(err)")
                }
            }
        }
    }
    
    /// 处理上报响应
    private func handleReportResponse(_ result: Result<GoldCoinWatchVideoResponse, APIError>, reportedIncrement: Int) {
        switch result {
        case .success(let response):
            handleSuccessResponse(response, reportedIncrement: reportedIncrement)
        case .failure(let error):
            print("[GoldAck] fail: \(error)")
            // 静默处理错误，不影响用户体验
            onError?("上报失败: \(error.localizedDescription)")
        }
    }
    
    /// 处理成功响应
    private func handleSuccessResponse(_ response: GoldCoinWatchVideoResponse, reportedIncrement: Int) {
        if response.status == 500 {
            // 错误状态：视频已完整观看过或其他错误
            print("[GoldAck] error: \(response.msg)")

            // 标记为已完成，停止继续计时和上报
            isCurrentVideoCompleted = true
            onTaskCompleted?(response.msg)

            // 停止计时器和观看
            stopWatching()

            onError?(response.msg)
            return
        }

        guard response.isSuccess, let data = response.data else {
            print("[GoldCoinVideoWatch] 响应数据异常")
            onError?("响应数据异常")
            return
        }

        let claimed = data.claimedRewards.reduce(0) { $0 + $1.rewardValue }
        print("[GoldAck] ok: totalSeconds=\(data.totalSeconds) videoCompleted=\(data.videoCompleted) rewardCoins=\(claimed)")

        // 仅用于调试：记录累计时长（与上报无关）
        accumulatedWatchTime = TimeInterval(data.totalSeconds)

        // 乐观推进阶段内秒数（避免等待配置刷新）
        if var cfg = currentVideoConfig {
            let total = cfg.conditionValue
            let cur = min(total, (cfg.viewingSeconds ?? 0) + reportedIncrement)
            cfg.viewingSeconds = cur
            currentVideoConfig = cfg
            let progress = total > 0 ? min(Double(cur) / Double(total), 1.0) : 0

            // 调试日志：记录进度更新
            print("[GoldWatch] 乐观更新进度: \(cur)/\(total) (\(String(format: "%.1f", progress * 100))%)")
            onProgressUpdate?(progress, cur, total)
        }

        // 更新当前视频进度
        if var p = currentVideoProgress {
            p.currentProgress = min(p.totalDuration, p.currentProgress + data.addedSeconds)
            p.completed = data.videoCompleted
            currentVideoProgress = p
        }

        // 不再在前端推进基线；奖励后通过拉配置获得新的阶段总长与当前值

        // 检查是否完成任务（当前视频完整观看）
        if data.videoCompleted {
            isCurrentVideoCompleted = true
            onTaskCompleted?(data.message)

            // 检查是否有奖励
            if !data.claimedRewards.isEmpty {
                let totalReward = data.claimedRewards.reduce(0) { $0 + $1.rewardValue }
                print("[GoldReward] coins=\(totalReward) -> \(data.message)")
                onRewardEarned?(totalReward, data.message)
            }

            // 停止计时
            stopWatching()
        } else {
            // 继续观看，更新UI
            updateProgressUI()

            // 奖励发放后由任务配置刷新下一阶段（VC/manager已触发配置刷新）
        }

        // 若本次上报跨越或恰好抵达阈值（reportedIncrement >= 剩余秒数），
        // 乐观将本地阶段秒数归零以避免下次再补1秒；稍后配置刷新会对齐真实值
        if let remaining = pendingReportedRemaining, reportedIncrement >= remaining {
            if var cfg = self.currentVideoConfig { cfg.viewingSeconds = 0; self.currentVideoConfig = cfg }
        }
        pendingReportedRemaining = nil
        // 轻量拉取一次全局任务配置，以对齐阶段内 viewingSeconds/conditionValue
        // 同步记录服务端基准时间（随后会被配置回调再对齐一次）
        self.lastServerProgressUpdateAt = Date()
        fetchAndNotifyGlobalConfig()
    }

    /// 计算本次自适应上报的秒数，避免跨阶段时出现下一阶段非0的问题
    private func computeAdaptiveReportSeconds() -> Int {
        // 兼容旧逻辑（不再使用固定Timer调用）
        guard let cfg = currentVideoConfig, cfg.conditionValue > 0 else {
            pendingReportedRemaining = nil
            return reportIntervalSeconds
        }
        let total = cfg.conditionValue
        let current = cfg.viewingSeconds ?? 0
        let remaining = max(0, total - current)
        pendingReportedRemaining = remaining
        if remaining == 0 { return 1 }
        return min(reportIntervalSeconds, remaining)
    }

    /// 轻量拉配置并回调，用于对齐全局 viewingSeconds
    private func fetchAndNotifyGlobalConfig() {
        APIManager.shared.getTaskWatchVideoRewardConfig { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    guard response.isSuccess, let cfg = response.data else { return }
                    self?.currentVideoConfig = cfg
                    let total = cfg.conditionValue
                    let current = cfg.viewingSeconds ?? 0
                    let state = cfg.state
                    if total > 0 {
                        if state == 2 {
                            self?.isGlobalTaskCompleted = true
                            self?.onProgressUpdate?(1.0, total, total)
                        } else {
                            self?.isGlobalTaskCompleted = false
                            let progress = min(Double(current) / Double(total), 1.0)
                            self?.onProgressUpdate?(progress, current, total)
                        }
                    }
                    // 对齐服务端进度的基准时间
                    self?.lastServerProgressUpdateAt = Date()
                case .failure:
                    // 静默失败，不打断观看逻辑
                    break
                }
            }
        }
    }
    
    /// 更新进度UI
    private func updateProgressUI() {
        // 这里不更新UI，因为全局任务进度由GoldCoinView自己管理
        // 只在上报成功时通过onProgressUpdate回调更新
        // no noisy log here
    }
    
    /// 重置状态
    private func resetState() {
        currentVideoId = nil
        accumulatedWatchTime = 0
        lastRecordTime = nil
        isWatching = false
        currentVideoConfig = nil
        currentVideoProgress = nil
        isCurrentVideoCompleted = false
        pendingBufferSeconds = 0
        pendingFractionalSeconds = 0
        didProbeOnStart = false
        // 不要在这里重置 isGlobalTaskCompleted，它由配置接口控制
    }
    
    // MARK: - Public Getters
    
    /// 获取当前观看状态
    var isCurrentlyWatching: Bool {
        return isWatching
    }
    
    /// 获取当前视频ID
    var currentWatchingVideoId: String? {
        return currentVideoId
    }
    
    /// 获取当前累计时长
    var currentAccumulatedTime: TimeInterval {
        return accumulatedWatchTime
    }

    // 不再需要基线对外暴露

    // MARK: - Debug Methods

    /// 获取管理器状态信息（用于调试）
    func getDebugInfo() -> String {
        var info = "=== GoldCoinVideoWatchManager 状态 ===\n"
        info += "当前视频ID: \(currentVideoId ?? "无")\n"
        info += "观看状态: \(isWatching ? "观看中" : "未观看")\n"
        info += "累计时长: \(accumulatedWatchTime)秒\n"
        info += "任务完成: \(isCurrentVideoCompleted ? "是" : "否")\n"

        if let config = currentVideoConfig {
            info += "任务配置: \(config.conditionValue)秒可获得\(config.rewardValue)金币\n"
        } else {
            info += "任务配置: 未加载\n"
        }

        if let progress = currentVideoProgress {
            info += "服务器进度: \(progress.currentProgress)/\(progress.totalDuration)秒\n"
            info += "服务器完成状态: \(progress.completed ? "已完成" : "进行中")\n"
        } else {
            info += "服务器进度: 未加载\n"
        }

        return info
    }

    /// 强制重置状态（用于测试）
    func forceReset() {
        resetState()
        print("[GoldCoinVideoWatch] 强制重置状态完成")
    }
}
