//
//  GoldCoinSegmentedReportTest.swift
//  <PERSON><PERSON><PERSON>qi
//
//  Created by Augment Agent on 2025/9/12.
//

import Foundation

/// 金币分段上报测试类
/// 用于验证新的服务器驱动分段上报机制
class GoldCoinSegmentedReportTest {
    
    // MARK: - 测试配置
    private let testVideoId = "test_video_segmented_123"
    
    // MARK: - 测试方法
    
    /// 测试分段策略计算
    func testSegmentStrategy() {
        print("=== 测试分段策略计算 ===")
        
        let testCases = [
            (duration: 10, expectedSegments: 5, expectedInterval: 2),  // 10秒 = 5段 x 2秒
            (duration: 12, expectedSegments: 4, expectedInterval: 3),  // 12秒 = 4段 x 3秒
            (duration: 15, expectedSegments: 5, expectedInterval: 3),  // 15秒 = 5段 x 3秒
            (duration: 8, expectedSegments: 4, expectedInterval: 2),   // 8秒 = 4段 x 2秒
            (duration: 9, expectedSegments: 3, expectedInterval: 3),   // 9秒 = 3段 x 3秒
            (duration: 7, expectedSegments: 7, expectedInterval: 1),   // 7秒 = 7段 x 1秒（无法被2或3整除）
        ]
        
        for testCase in testCases {
            let result = calculateSegmentStrategy(validDuration: testCase.duration)
            print("时长\(testCase.duration)秒 -> \(result.segments)段 x \(result.interval)秒 (期望: \(testCase.expectedSegments)段 x \(testCase.expectedInterval)秒)")
            
            if result.segments == testCase.expectedSegments && result.interval == testCase.expectedInterval {
                print("✅ 通过")
            } else {
                print("❌ 失败")
            }
        }
        
        print("分段策略测试完成\n")
    }
    
    /// 计算分段策略（复制自管理器的逻辑）
    private func calculateSegmentStrategy(validDuration: Int) -> (segments: Int, interval: Int) {
        var segmentInterval: Int
        var totalSegments: Int
        
        // 优先使用2的倍数分段
        if validDuration % 2 == 0 {
            segmentInterval = 2
            totalSegments = validDuration / 2
        } else if validDuration % 3 == 0 {
            segmentInterval = 3
            totalSegments = validDuration / 3
        } else {
            // 如果不能被2或3整除，使用最接近的策略
            if validDuration <= 6 {
                segmentInterval = 1
                totalSegments = validDuration
            } else if validDuration % 2 == 1 {
                // 奇数，优先用3分段
                segmentInterval = 3
                totalSegments = (validDuration / 3) + (validDuration % 3 > 0 ? 1 : 0)
            } else {
                // 偶数，用2分段
                segmentInterval = 2
                totalSegments = validDuration / 2
            }
        }
        
        return (totalSegments, segmentInterval)
    }
    
    /// 测试管理器状态
    func testManagerState() {
        print("=== 测试管理器状态 ===")
        
        let manager = GoldCoinSegmentedReportManager.shared
        
        print("初始状态:")
        print(manager.getDebugInfo())
        
        // 模拟开始观看
        print("开始观看测试视频...")
        manager.startWatching(videoId: testVideoId, isNoteType: false)
        
        print("观看开始后状态:")
        print(manager.getDebugInfo())
        
        // 模拟暂停
        print("暂停观看...")
        manager.pauseWatching()
        
        print("暂停后状态:")
        print(manager.getDebugInfo())
        
        // 模拟恢复
        print("恢复观看...")
        manager.resumeWatching()
        
        print("恢复后状态:")
        print(manager.getDebugInfo())
        
        // 模拟停止
        print("停止观看...")
        manager.stopWatching()
        
        print("停止后状态:")
        print(manager.getDebugInfo())
        
        print("管理器状态测试完成\n")
    }
    
    /// 测试回调机制
    func testCallbacks() {
        print("=== 测试回调机制 ===")
        
        let manager = GoldCoinSegmentedReportManager.shared
        
        // 设置回调
        manager.onProgressUpdate = { progress, current, total in
            print("📊 进度更新: \(String(format: "%.1f", progress * 100))% (\(current)/\(total))")
        }
        
        manager.onRewardEarned = { coins, message in
            print("🪙 获得奖励: \(coins)金币 - \(message)")
        }
        
        manager.onTaskCompleted = { message in
            print("✅ 任务完成: \(message)")
        }
        
        manager.onError = { error in
            print("❌ 错误: \(error)")
        }
        
        print("回调设置完成")
        print("回调机制测试完成\n")
    }
    
    /// 测试笔记类型处理
    func testNoteTypeHandling() {
        print("=== 测试笔记类型处理 ===")
        
        let manager = GoldCoinSegmentedReportManager.shared
        
        print("测试笔记类型视频...")
        manager.startWatching(videoId: "note_video_123", isNoteType: true)
        
        print("笔记类型处理后状态:")
        print(manager.getDebugInfo())
        
        print("笔记类型处理测试完成\n")
    }
    
    /// 运行所有测试
    func runAllTests() {
        print("🚀 开始金币分段上报机制测试")
        print("新机制特点:")
        print("1. 试探1秒获取有效时长")
        print("2. 按2或3的倍数分段上报")
        print("3. 完全服务器驱动UI更新")
        print("4. 定时器跟随播放状态")
        print("5. 移除所有本地进度计算")
        print("")
        
        testSegmentStrategy()
        testManagerState()
        testCallbacks()
        testNoteTypeHandling()
        
        print("✅ 所有测试完成")
        print("")
        print("预期效果:")
        print("- 金币进度完全由服务器控制，永远合法")
        print("- 不会出现进度倒退或突然跳满")
        print("- 网络延迟只影响进度更新时机，不影响准确性")
        print("- 用户体验更稳定，服务器数据更可靠")
    }
}

// MARK: - 使用示例
extension GoldCoinSegmentedReportTest {
    
    /// 在VideoDisplayCenterViewController中调用此方法进行测试
    static func performSegmentedReportTest() {
        let test = GoldCoinSegmentedReportTest()
        test.runAllTests()
    }
    
    /// 快速测试分段策略
    static func quickTestSegmentStrategy() {
        let test = GoldCoinSegmentedReportTest()
        test.testSegmentStrategy()
    }
}
