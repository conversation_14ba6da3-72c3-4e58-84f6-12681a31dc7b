//
//  GoldCoinSystemTaskCenterViewController.swift
//  Shuxia<PERSON>qi
//
//  Created by yongsheng ye on 2025/8/12.
//

import UIKit
import SnapKit
import CoreImage

// MARK: - 金币系统任务中心控制器
class GoldCoinSystemTaskCenterViewController: BaseViewController {

    // MARK: - 数据模型
    private var currentGoldCoins: Int = 0
    private var claimedGoldCoinsToday: Int = 0
    private let defaultRewardCoins: Int = 888
    private var dailyEarnableCoins: Int = 2000
    private var exchangeRate: String = "10000金币=1元现金"
    
    // 任务奖励配置数据
    private var taskRewardConfigs: [TaskRewardConfig] = []
    
    // 加载状态
    private var isLoadingTaskConfig: Bool = false

    // 签到数据
    private var checkInDays: [CheckInDay] = []
    private var consecutiveCheckInDays: Int = 2 // 当前连续签到天数（0表示今天还没签到）
    // 今日是否已点击过签到按钮（由外部传入，后续由服务端控制）
    private var hasClickedCheckInButtonToday: Bool = false
    // 是否已从API加载签到数据
    private var hasLoadedCheckInDataFromAPI: Bool = false

    // 观看视频进度
    private var videoWatchProgress: Float = 0.3 // 30%
    private var videoWatchedSeconds: Int = 180 // 已观看3分钟
    private var videoTotalSeconds: Int = 600 // 总共10分钟

    // MARK: - UI 组件

    // 滚动视图
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.backgroundColor = .white
        scrollView.showsVerticalScrollIndicator = false
        
        // 添加下拉刷新
        let refreshControl = UIRefreshControl()
        refreshControl.addTarget(self, action: #selector(handleRefresh(_:)), for: .valueChanged)
        scrollView.refreshControl = refreshControl
        
        return scrollView
    }()

    // 滚动内容容器
    private lazy var scrollContentView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()

    // 头部背景容器 - #F7F7F7色，20圆角，168高度
    private lazy var headerBackgroundView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#F7F7F7")
        view.layer.cornerRadius = 20
        return view
    }()

    // 当前金币背景图 - 橙色背景
    private lazy var goldCoinBackgroundView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "gold_coin_task_center_header_bg") // 预命名的橙色背景图
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 16
        imageView.isUserInteractionEnabled = true
        return imageView
    }()

    // 当前金币标题
    private lazy var currentGoldTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "当前金币"
        label.textColor = .white
        label.font = .systemFont(ofSize: 14, weight: .medium)
        return label
    }()

    // 金币数量
    private lazy var goldCoinAmountLabel: UILabel = {
        let label = UILabel()
        label.text = "\(currentGoldCoins)"
        label.textColor = .white
        label.font = .systemFont(ofSize: 24, weight: .bold)
        return label
    }()

    // 提现按钮
    private lazy var withdrawButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("提现", for: .normal)
        button.setTitleColor(.appThemeOrange, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14, weight: .medium)
        button.backgroundColor = .white
        button.layer.cornerRadius = 16
        button.addTarget(self, action: #selector(withdrawButtonTapped), for: .touchUpInside)
        return button
    }()

    // 今日已赚标签
    private lazy var dailyEarnLabel: UILabel = {
        let label = UILabel()
        label.text = "今日已赚 \(dailyEarnableCoins)金币"
        label.textColor = .white
        label.font = .systemFont(ofSize: 14)
        return label
    }()

    // 兑换比例标签
    private lazy var exchangeRateLabel: UILabel = {
        let label = UILabel()
        label.text = exchangeRate
        label.textColor = UIColor(hex: "#666666")
        label.font = .systemFont(ofSize: 14)
        label.backgroundColor = .clear
        label.textAlignment = .left
        return label
    }()

    // MARK: - 动态任务容器
    
    // 任务容器的垂直堆栈视图
    private lazy var taskStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 0
        stackView.distribution = .fill
        return stackView
    }()
    
    // 存储动态创建的任务视图
    private var taskViews: [TaskItemView] = []
    
    // 签到日历容器（复用现有逻辑）
    private var checkInCalendarView: UIView?
    
    // 视频进度容器（复用现有逻辑）
    private var videoProgressView: UIView?
    
    // 活跃度进度容器
    private var activityProgressView: UIView?

    // 悬浮礼盒按钮（可长按拖动）
    private var giftButton: DraggableGiftButton?
    // 弹窗弱引用，避免重复创建
    private weak var currentGoldPopup: GoldCoinPopupView?
    // 日签弹窗弱引用，避免重复创建
    private weak var currentCheckInPopup: DailyAttendancePopupView?

    // MARK: - 活跃度宝箱相关属性

    // 活跃度数据
    private var activeProgressData: ActiveProgressData?
    // 下一个活跃度奖励配置
    private var nextActiveReward: ActiveRewardConfig?
    // 是否已经初始化过活跃度（首次进入需要上报0秒）
    private var hasInitializedActivity: Bool = false
    // UI更新定时器
    private var uiUpdateTimer: Timer?

    // 测试定时器（用于调试）
    private var testTimer: Timer?
    // 统一的倒计时显示值（用于按钮和弹窗同步）
    private var unifiedDisplaySeconds: Int?
    // 上次倒计时更新时间戳，用于统一显示秒数的平滑递减
    private var lastCountdownUpdateTime: Date?
    // 页内上报定时器（每3秒上报3秒）
    private var pageReportTimer: Timer?
    private var isPageReporting: Bool = false

    // 右下角宝箱按钮的"显示用剩余秒数"，每次UI tick最多只减少1，保证视觉平滑
    private var giftDisplayRemainingSeconds: Int?
    // 上次对礼盒显示用剩余秒数做"减1"的时间戳，用于限速（<=1次/秒）
    private var giftLastDisplayTick: Date?

    // 本地倒计时到0后的“容错快速校准”标记，避免重复并发
    private var isReconcilingAfterLocalEnd: Bool = false
    private let reconcileToleranceSeconds: Int = 10

    // MARK: - 生命周期
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupData()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        // 监听活跃度进度更新通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleActivityProgressUpdated(_:)),
            name: ActivityProgressManager.activityProgressUpdatedNotification,
            object: nil
        )

        // 监听应用前后台切换，确保页面外累计准确
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleAppWillResignActive),
            name: UIApplication.willResignActiveNotification,
            object: nil
        )
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleAppDidBecomeActive),
            name: UIApplication.didBecomeActiveNotification,
            object: nil
        )

        // 标记进入任务中心（停止页面外累计并结算缓存）
        ActivityProgressManager.shared.markEnterTaskCenterPage()

        // 启动UI更新定时器（每秒更新一次倒计时）
        startUIUpdateTimer()

        // 启动进入页面后的同步与上报流程
        startEnterTaskCenterFlow()
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        // 移除所有通知监听
        NotificationCenter.default.removeObserver(self)

        // 停止页内上报定时器
        stopPageReportTimer()

        // 标记离开任务中心（开始页面外累计）
        ActivityProgressManager.shared.markLeaveTaskCenterPage()

        // 停止UI更新定时器
        stopUIUpdateTimer()

        // 停止测试定时器
        stopTestTimer()

        // 隐藏可能存在的HUD菊花
        hideLoadingIndicator()
    }

    // MARK: - 签到状态注入（后续由服务器控制）
    func configureCheckIn(consecutiveDays: Int, hasClickedToday: Bool) {
        consecutiveCheckInDays = max(0, min(7, consecutiveDays))
        hasClickedCheckInButtonToday = hasClickedToday

        // 只有在没有API数据时才使用硬编码数据
        if !hasLoadedCheckInDataFromAPI {
            print("[TaskCenter] 使用硬编码签到数据（API数据尚未加载）")
            generateCheckInDays()
        } else {
            print("[TaskCenter] 保留现有API签到数据，不使用硬编码数据")
            // 只更新连续签到天数，不重新生成数据
            updateCheckInStatus()
        }
    }

    // 兼容旧的测试方法
    func setTestConsecutiveCheckInDays(_ days: Int) {
        configureCheckIn(consecutiveDays: days, hasClickedToday: false)
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)

        // 初始化悬浮礼盒按钮
        setupGiftButtonIfNeeded()
        
        // 获取金币数据
        fetchGoldCoinData()
        
        // 获取任务奖励配置
        fetchTaskRewardConfig()

        // 活跃度数据在 startEnterTaskCenterFlow 中处理

        // 测试活跃度功能（仅用于调试，正式版本应移除）
        #if DEBUG
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            self.testActivityFeatures()
        }
        #endif

    }

    // MARK: - 数据获取方法
    
    func fetchGoldCoinData() {
        APIManager.shared.getGoldCoinLoadingData { [weak self] result in
            switch result {
            case .success(let response):
                if response.isSuccess, let data = response.data {
                    // 更新当前金币数量
                    self?.currentGoldCoins = data.number
                    // 更新今日已领取金币数量
                    self?.claimedGoldCoinsToday = data.claimGoldCoinsTodayNumber
                    DispatchQueue.main.async {
                        self?.goldCoinAmountLabel.text = "\(data.number)"
                        // 更新今日已赚标签
                        self?.dailyEarnLabel.text = "今日已赚 \(data.claimGoldCoinsTodayNumber)金币"
                    }
                } else {
                    print("获取金币数据失败: \(response.displayMessage)")
                }
            case .failure(let error):
                print("获取金币数据请求失败: \(error.localizedDescription)")
            }
        }
    }
    
    private func fetchTaskRewardConfig() {
        guard !isLoadingTaskConfig else { return }
        
        isLoadingTaskConfig = true
        showLoadingIndicator()
        
        APIManager.shared.getTaskRewardConfig { [weak self] result in
            DispatchQueue.main.async {
                self?.isLoadingTaskConfig = false
                self?.hideLoadingIndicator()
                
                switch result {
                case .success(let response):
                    if response.status == 200 {
                        print("[TaskCenter] 成功获取任务奖励配置，任务数量: \(response.data.count)")
                        for (index, config) in response.data.enumerated() {
                            print("[TaskCenter] 任务\(index): 类型=\(config.conditionType), 名称=\(config.conditionName), 奖励=\(config.rewardValue)")
                            if config.conditionType == TaskConditionType.continuousSignIn.rawValue {
                                print("[TaskCenter] 签到任务详情: conditionValue=\(config.conditionValue), signRewardConfigList数量=\(config.signRewardConfigList?.count ?? 0)")
                            }
                        }
                        self?.taskRewardConfigs = response.data
                        self?.updateTaskUIWithConfig()
                    } else {
                        print("获取任务奖励配置失败: \(response.errMsg)")
                        self?.showErrorMessage("获取任务配置失败，请稍后重试")
                    }
                case .failure(let error):
                    print("获取任务奖励配置请求失败: \(error.localizedDescription)")
                    self?.showErrorMessage("网络连接失败，请检查网络后重试")
                }
            }
        }
    }
    
    private func updateTaskUIWithConfig() {
        // 清空现有任务视图
        clearTaskViews()

        // 过滤掉活跃度任务（conditionType = 7），它属于右下角宝箱功能
        let displayTasks = taskRewardConfigs.filter { $0.conditionType != 7 }

        // 根据API返回的顺序动态创建任务视图
        for (index, config) in displayTasks.enumerated() {
            let taskView = createTaskView(for: config, at: index)
            taskViews.append(taskView)
            taskStackView.addArrangedSubview(taskView)

            // 添加分割线（最后一个任务不需要分割线）
            if index < displayTasks.count - 1 {
                let separator = createSeparatorView()
                taskStackView.addArrangedSubview(separator)
            }

            // 如果是签到任务，更新签到日历数据
            if config.conditionType == TaskConditionType.continuousSignIn.rawValue {
                print("[TaskCenter] 发现签到任务配置，conditionValue: \(config.conditionValue)")
                if let signRewardList = config.signRewardConfigList {
                    print("[TaskCenter] 签到奖励配置列表存在，数量: \(signRewardList.count)")
                    updateSignInCalendar(signRewardList: signRewardList, currentDay: config.conditionValue)
                } else {
                    print("[TaskCenter] ⚠️ 签到任务配置中缺少 signRewardConfigList")
                }
            }
        }

        // 更新今日已赚金币数量
        updateDailyEarnableCoins()
    }

    // MARK: - 活跃度数据获取方法

    private func fetchActiveProgressData() {
        // 使用活跃度管理器刷新数据
        ActivityProgressManager.shared.refreshActiveData()

        // 获取当前数据并更新UI
        if let data = ActivityProgressManager.shared.getCurrentActiveData() {
            activeProgressData = data
            updateGiftButtonWithActiveData()
        }

        // 首次进入且首个宝箱conditionValue为0时，需要上报0秒激活
        handleFirstTimeActivation()
    }

    private func handleFirstTimeActivation() {
        guard !hasInitializedActivity else { return }

        // 检查当前未领取奖励或下一个奖励配置是否为首个宝箱（conditionValue为0）
        let manager = ActivityProgressManager.shared
        if let currentReward = manager.getCurrentRewardConfig(),
           currentReward.conditionValue == 0 {
            // 使用活跃度管理器上报0秒激活首个宝箱
            print("[TaskCenter] 检测到首个宝箱(conditionValue=0)，上报0秒激活")
            ActivityProgressManager.shared.reportProgress(seconds: 0)
        }

        hasInitializedActivity = true
    }

    private func claimActiveReward(configId: Int) {
        APIManager.shared.claimActiveReward(configId: configId) { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if response.isSuccess, let data = response.data {
                        // 更新活跃度数据
                        self?.activeProgressData = data
                        // 同步更新活跃度管理器的数据
                        ActivityProgressManager.shared.updateCurrentActiveData(data)
                        // 更新宝箱按钮UI
                        self?.updateGiftButtonWithActiveData()
                        // 刷新金币数量
                        self?.fetchGoldCoinData()
                        // 显示成功提示
                        self?.showHUD("领取成功！")

                        print("[TaskCenter] 领取成功，已更新到下一个宝箱状态")
                        self?.logCurrentRewardStatus()

                        // 依据最新服务端状态，决定是否开启新一轮页内上报（或停止）
                        self?.evaluateAndHandleStatus(using: data, source: "领取成功")
                    } else {
                        self?.showHUD(response.displayMessage)
                    }
                case .failure(let error):
                    self?.showHUD("领取失败：\(error.localizedDescription)")
                }
            }
        }
    }

    /// 打印当前奖励状态（用于调试）
    private func logCurrentRewardStatus() {
        let manager = ActivityProgressManager.shared
        if let currentReward = manager.getCurrentRewardConfig() {
            let canClaim = manager.hasAvailableRewards()
            let remaining = manager.getCurrentRewardRemainingSeconds() ?? 0
            let totalActive = manager.getCurrentTotalActiveSeconds()
            print("   - 当前宝箱: ID=\(currentReward.id), 奖励=\(currentReward.rewardValue)金币")
            print("   - 实时总活跃时长: \(totalActive)秒")
            print("   - 可领取: \(canClaim), 剩余时间: \(remaining)秒")
            print("   - 按钮文案: \(getActiveGiftTitle())")
        }
    }
    
    private func clearTaskViews() {
        // 移除所有任务视图
        taskViews.forEach { $0.removeFromSuperview() }
        taskViews.removeAll()
        
        // 清空堆栈视图
        taskStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }
        
        // 清理特殊容器
        checkInCalendarView?.removeFromSuperview()
        checkInCalendarView = nil
        videoProgressView?.removeFromSuperview()
        videoProgressView = nil
        activityProgressView?.removeFromSuperview()
        activityProgressView = nil
    }
    
    private func createTaskView(for config: TaskRewardConfig, at index: Int) -> TaskItemView {
        let taskView = TaskItemView()
        taskView.configure(with: config)
        
        // 设置点击事件
        taskView.onButtonTapped = { [weak self] in
            self?.handleTaskTapped(config: config)
        }
        
        return taskView
    }
    
    private func createSeparatorView() -> UIView {
        let separator = UIView()
        separator.backgroundColor = UIColor(hex: "#E5E5E5")
        
        // 创建一个容器视图来包装分割线
        let containerView = UIView()
        containerView.addSubview(separator)
        
        separator.snp.makeConstraints { make in
            make.height.equalTo(1)
            make.left.right.equalToSuperview().inset(16)
            make.top.bottom.equalToSuperview()
        }
        
        return containerView
    }
    
    private func handleTaskTapped(config: TaskRewardConfig) {
        // 检查任务状态，如果已完成待领取(state == 1)，则调用对应的领取API
        if config.state == 1 {
            // 任务已完成，可以领取奖励
            claimTaskReward(config: config)
            return
        }
        
        // 未完成任务，根据任务类型跳转到对应页面
        switch TaskConditionType(rawValue: config.conditionType) {
        case .firstPublishVideo, .publishVideo:
            publishVideoTaskTapped()
        case .continuousSignIn:
            checkInTaskTapped()
        case .shareVideo:
            shareTaskTapped()
        case .inviteFriends:
            inviteFriendsTaskTapped()
        case .videoPlay:
            watchVideoTaskTapped()
        case .activity:
            // 活跃度任务不应该出现在列表中，这里是保护性代码
            print("活跃度任务属于右下角宝箱功能，不应在任务列表中显示")
        default:
            print("未知任务类型: \(config.conditionType)")
        }
    }
    
    /// 领取任务奖励
    private func claimTaskReward(config: TaskRewardConfig) {
        print("[TaskCenter] 开始领取任务奖励，任务类型: \(config.conditionType), 奖励: \(config.rewardValue)金币")
        
        switch TaskConditionType(rawValue: config.conditionType) {
        case .firstPublishVideo:
            // 首次发布视频奖励
            claimFirstPublishReward()
        case .continuousSignIn:
            // 签到奖励
            performCheckIn()
        case .shareVideo, .inviteFriends, .videoPlay, .publishVideo:
            // 其他任务类型的奖励领取（如果有对应的API）
            showHUD("正在领取奖励...")
            // TODO: 添加其他任务类型的领取API
        case .activity:
            // 活跃度奖励在宝箱功能中处理
            showHUD("请通过右下角宝箱领取活跃度奖励")
        default:
            print("未知任务类型的奖励领取: \(config.conditionType)")
            showHUD("领取失败：未知的任务类型")
        }
    }
    
    /// 领取首次发布视频奖励
    private func claimFirstPublishReward() {
        print("[TaskCenter] 开始领取首次发布视频奖励")
        showHUD("正在领取奖励...")
        
        APIManager.shared.receiveOneReleaseReward { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if response.status == 200 {
                        print("[TaskCenter] 首次发布视频奖励领取成功")
                        self?.showHUD("领取成功！")
                        // 刷新任务配置和金币数据
                        self?.fetchTaskRewardConfig()
                        self?.fetchGoldCoinData()
                    } else {
                        print("[TaskCenter] 首次发布视频奖励领取失败: \(response.errMsg)")
                        self?.showHUD(response.errMsg ?? "领取失败")
                    }
                case .failure(let error):
                    print("[TaskCenter] 首次发布视频奖励领取请求失败: \(error.localizedDescription)")
                    self?.showHUD("领取失败，请检查网络后重试")
                }
            }
        }
    }
    
    private func updateDailyEarnableCoins() {
        // 现在使用API返回的claimGoldCoinsTodayNumber字段
        // 这个方法保留用于兼容性，但实际数据已在fetchGoldCoinData中更新
        dailyEarnLabel.text = "今日已赚 \(claimedGoldCoinsToday)金币"
    }
    
    private func activityTaskTapped() {
        print("活跃度任务被点击")
        // TODO: 跳转到活跃度页面
        let alert = UIAlertController(title: "活跃度", message: "跳转到活跃度页面", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
    
    private func updateSignInCalendar(signRewardList: [SignRewardConfig], currentDay: Int) {
        // 清空现有数据
        checkInDays = []

        print("[TaskCenter] 更新签到日历数据，当前天数: \(currentDay), 奖励配置数量: \(signRewardList.count)")

        // 根据API返回的签到奖励配置更新签到日历
        for (index, signConfig) in signRewardList.enumerated() {
            let dayName = "第\(index + 1)天"
            let reward = signConfig.rewardValue
            // signConfig.state: 0-未签到 1-已签到
            let isCheckedIn = (signConfig.state == 1)
            checkInDays.append(CheckInDay(day: dayName, reward: reward, isCheckedIn: isCheckedIn))
            print("[TaskCenter] 第\(index + 1)天: 奖励=\(reward)金币, 已签到=\(isCheckedIn)")
        }

        // 更新当前连续签到天数
        consecutiveCheckInDays = currentDay
        // 标记已从API加载数据
        hasLoadedCheckInDataFromAPI = true
        print("[TaskCenter] 签到日历更新完成，连续签到天数: \(consecutiveCheckInDays)")
    }

    // MARK: - 导航辅助方法

    /// 安全的页面跳转方法，处理模态展示时没有导航控制器的情况
    private func pushViewControllerSafely(_ viewController: UIViewController) {
        if let navigationController = navigationController {
            // 如果有导航控制器，正常push
            navigationController.pushViewController(viewController, animated: true)
            print("使用导航控制器push跳转: \(type(of: viewController))")
        } else {
            // 如果没有导航控制器（模态展示情况），创建导航控制器并present
            let navController = UINavigationController(rootViewController: viewController)
            navController.modalPresentationStyle = .fullScreen
            
            // 设置导航栏样式
            if #available(iOS 13.0, *) {
                let appearance = UINavigationBarAppearance()
                appearance.configureWithOpaqueBackground()
                appearance.backgroundColor = .white
                navController.navigationBar.standardAppearance = appearance
                navController.navigationBar.scrollEdgeAppearance = appearance
            }
            
            present(navController, animated: true)
            print("创建导航控制器并模态展示: \(type(of: viewController))")
        }
    }

  // MARK: - 设置方法

    private func setupUI() {
        navTitle = "任务中心"
        contentView.backgroundColor = .white

        // 添加滚动视图
        contentView.addSubview(scrollView)
        scrollView.addSubview(scrollContentView)

        // 添加头部背景容器
        scrollContentView.addSubview(headerBackgroundView)

        // 添加当前金币背景图到头部背景容器
        headerBackgroundView.addSubview(goldCoinBackgroundView)
        goldCoinBackgroundView.addSubview(currentGoldTitleLabel)
        goldCoinBackgroundView.addSubview(goldCoinAmountLabel)
        goldCoinBackgroundView.addSubview(withdrawButton)
        goldCoinBackgroundView.addSubview(dailyEarnLabel)

        // 添加兑换比例标签到头部背景容器
        headerBackgroundView.addSubview(exchangeRateLabel)

        // 添加动态任务容器
        scrollContentView.addSubview(taskStackView)

        setupConstraints()
        setupData() // 先设置数据
    }

    private func setupData() {
        // 根据当前连续签到天数生成签到数据
        generateCheckInDays()
    }

    private func generateCheckInDays() {
        checkInDays = []
        // 固定展示 第1天 ~ 第7天；连续到第N天就亮前N天
        for i in 1...7 {
            let dayName = "第\(i)天"
            let reward = i * 10 // +10, +20, ... +70
            let isCheckedIn = i <= consecutiveCheckInDays
            checkInDays.append(CheckInDay(day: dayName, reward: reward, isCheckedIn: isCheckedIn))
        }
    }

    /// 更新签到状态（保留API奖励数据，只更新签到状态）
    private func updateCheckInStatus() {
        for i in 0..<checkInDays.count {
            checkInDays[i].isCheckedIn = (i + 1) <= consecutiveCheckInDays
        }
        print("[TaskCenter] 更新签到状态完成，连续签到天数: \(consecutiveCheckInDays)")
    }

    private func setupConstraints() {
        // 滚动视图约束
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        scrollContentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        // 头部背景容器约束 - 距离顶部16pt，左右各16pt，高度168pt
        headerBackgroundView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(168)
        }

        // 当前金币背景图约束 - 在头部背景容器上方
        goldCoinBackgroundView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(120) // 调整高度，为下方文案留空间
        }

        // 当前金币标题约束
        currentGoldTitleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.equalToSuperview().offset(16)
            make.height.equalTo(20)
        }

        // 金币数量约束
        goldCoinAmountLabel.snp.makeConstraints { make in
            make.top.equalTo(currentGoldTitleLabel.snp.bottom).offset(8)
            make.left.equalTo(currentGoldTitleLabel)
        }

        // 提现按钮约束
        withdrawButton.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.right.equalToSuperview().offset(-20)
            make.width.equalTo(74)
            make.height.equalTo(32)
        }

        // 今日已赚标签约束
        dailyEarnLabel.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-16)
            make.left.equalTo(currentGoldTitleLabel)
        }

        // 兑换比例标签约束 - 在当前金币背景图下方+8pt，左边距离背景View +16pt
        exchangeRateLabel.snp.makeConstraints { make in
            make.top.equalTo(goldCoinBackgroundView.snp.bottom).offset(8)
            make.left.equalToSuperview().offset(16)
            make.bottom.equalToSuperview().offset(-16)
        }

        // 动态任务容器约束
        taskStackView.snp.makeConstraints { make in
            make.top.equalTo(headerBackgroundView.snp.bottom).offset(16)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview().offset(-20)
        }
    }


    // 初始化并添加悬浮礼盒按钮
    private func setupGiftButtonIfNeeded() {
        // 已添加则不重复添加
        if let button = giftButton, button.superview != nil {
            contentView.bringSubviewToFront(button)
            return
        }

        let button = DraggableGiftButton()
        // 初始化文案
        button.setTitle(initialGiftTitle())
        button.addTarget(self, action: #selector(giftButtonTapped), for: .touchUpInside)

        contentView.addSubview(button)

        // 动态尺寸与初始位置（靠右下，留出一定边距；宽度按标题自适应）
        let defaultSize = button.intrinsicContentSize
        let height: CGFloat = defaultSize.height.isFinite ? defaultSize.height : 120
        let inset: CGFloat = 16
        let maxWidth = max(120, contentView.bounds.width - inset * 2)
        let width: CGFloat = button.preferredWidth(maxWidth: maxWidth)
        let x = contentView.bounds.width - inset - width
        let y = max(inset, contentView.bounds.height - height - 100)
        button.frame = CGRect(x: x, y: y, width: width, height: height)

        giftButton = button
        contentView.bringSubviewToFront(button)
    }

    @objc private func giftButtonTapped() {
        let manager = ActivityProgressManager.shared
        guard let currentReward = manager.getCurrentRewardConfig() else {
            showHUD("今日奖励已领完，明天再来")
            return
        }

        print("礼盒按钮被点击 -> 弹出活跃度宝箱弹窗")
        presentActiveRewardPopup(rewardConfig: currentReward)
    }

    /// 弹出活跃度奖励弹窗
    private func presentActiveRewardPopup(rewardConfig: ActiveRewardConfig) {
        // 若已存在，先移除旧的
        currentGoldPopup?.dismiss(animated: false)

        let manager = ActivityProgressManager.shared
        let canClaim = manager.hasAvailableRewards()
        let displaySeconds = getUnifiedDisplaySeconds()  // 使用统一的显示秒数

        let popup = GoldCoinPopupView()
        popup.onDismiss = { [weak self] in
            self?.currentGoldPopup = nil
        }

        if canClaim {
            // 可领取状态 - 显示领取弹窗
            popup.configure(mode: .reward,
                          valueText: "\(rewardConfig.rewardValue)金币",
                          subtitleText: "恭喜获得奖励！")
            popup.onConfirm = { [weak self] in
                self?.handleActiveRewardClaim(configId: rewardConfig.id)
            }
        } else {
            // 倒计时状态 - 使用统一的显示秒数
            if displaySeconds <= 0 {
                // 本地已到0：触发快速校准，不再让用户重新等3s
                popup.configure(mode: .countdown,
                                valueText: "00:00",
                                subtitleText: nil)
                popup.onConfirm = { }
                reconcileAfterLocalCountdownIfNeeded(source: "popup_open")
            } else {
                popup.configure(mode: .countdown,
                                valueText: formatSeconds(displaySeconds),
                                subtitleText: "活跃度倒计时结束即可获得\(rewardConfig.rewardValue)金币！")
                // 倒计时状态下点击不做任何操作，减少文字打扰
                popup.onConfirm = { }
            }
        }

        popup.show(in: self.view)
        currentGoldPopup = popup
    }

    /// 处理活跃度奖励领取
    private func handleActiveRewardClaim(configId: Int) {
        // 先关闭弹窗
        currentGoldPopup?.dismiss(animated: true)

        // 调用领取API
        claimActiveReward(configId: configId)
    }

//    // 展示金币弹窗（左右24pt，按327:436动态高度） - 旧版依赖全局倒计时逻辑
//    private func presentGoldCoinPopup() {
//        // 若已存在，先移除旧的
//        currentGoldPopup?.dismiss(animated: false)
//
//        // 达到每日上限：不弹窗，直接 HUD
//        let mgr = GoldCountdownManager.shared
//        if mgr.isDailyLimitReached {
//            updateGiftButtonTitle("明天再来")
//            showHUD("3-今日领取数量已达上限，请明日再来~")
//            return
//        }
//
//        let popup = GoldCoinPopupView()
//        popup.onDismiss = { [weak self] in
//            self?.currentGoldPopup = nil
//        }
//        // 根据当前状态配置
//        if mgr.isRunning {
//            let reward = mgr.rewardCoins > 0 ? mgr.rewardCoins : defaultRewardCoins
//            popup.configure(mode: .countdown, valueText: formatSeconds(mgr.remainingSeconds), subtitleText: "即可获得\(reward)金币！")
//        } else {
//            let reward = currentRewardCoins()
//            popup.configure(mode: .reward, valueText: "\(reward)金币")
//        }
//        popup.onConfirm = { [weak self] in
//            self?.handlePopupConfirm()
//        }
//        popup.show(in: self.view)
//        currentGoldPopup = popup
//    }

    // 对外提供：更新礼盒按钮文案
    func updateGiftButtonTitle(_ text: String) {
        guard let button = giftButton else { return }
        button.setTitle(text)
        // 标题变化后同步调整宽度，保持在父视图边界内
        let inset: CGFloat = 8
        let maxWidth = max(120, contentView.bounds.width - inset * 2)
        let newWidth = button.preferredWidth(maxWidth: maxWidth)
        var frame = button.frame
        let oldCenter = button.center
        frame.size.width = newWidth
        button.frame = frame
        // 约束中心点不越界（水平）
        let halfW = newWidth * 0.5
        let minX = inset + halfW
        let maxX = contentView.bounds.width - inset - halfW
        let clampedX = min(max(oldCenter.x, minX), maxX)
        button.center = CGPoint(x: clampedX, y: oldCenter.y)
    }

    // MARK: - 活跃度宝箱逻辑
    private func initialGiftTitle() -> String {
        return getActiveGiftTitle()
    }

    private func getActiveGiftTitle() -> String {
        let manager = ActivityProgressManager.shared

        // 获取当前宝箱配置
        guard let currentReward = manager.getCurrentRewardConfig() else {
            return activeProgressData == nil ? "加载中..." : "明天再来"
        }

        // 检查是否有可领取的奖励
        if manager.hasAvailableRewards() {
            // 可领取时重置统一显示秒数
            unifiedDisplaySeconds = nil
            lastCountdownUpdateTime = nil
            let rewardValue = manager.getCurrentAvailableReward()
            return "点击查看 \(rewardValue)金币"
        }

        // 获取统一的倒计时显示值
        let displaySeconds = getUnifiedDisplaySeconds()
        if displaySeconds > 0 {
            return "\(formatSeconds(displaySeconds)) \(currentReward.rewardValue)金币"
        }

        // 本地显示到0但不可领取：保持显示 00:00 与弹窗一致，减少误导
        return "00:00 \(currentReward.rewardValue)金币"
    }

    /// 获取统一的倒计时显示值（用于按钮和弹窗同步）
    /// 规则：
    /// - 单调递减，不允许向上跳（避免 00:59 -> 01:00 闪烁）
    /// - 若服务端/平滑值比本地更小且差距>2s，立刻向下对齐（追赶真实进度）
    /// - 若服务端比本地更大（落后）则忽略上跳，继续以本地节奏递减
    private func getUnifiedDisplaySeconds() -> Int {
        let manager = ActivityProgressManager.shared

        // 实际剩余（优先平滑），无则为0
        let actual = manager.getSmoothedRewardRemainingSeconds() ?? manager.getCurrentRewardRemainingSeconds() ?? 0

        // 无剩余：清空缓存
        if actual <= 0 {
            unifiedDisplaySeconds = nil
            lastCountdownUpdateTime = nil
            return 0
        }

        let now = Date()

        // 初始化：以实际值为准
        guard var display = unifiedDisplaySeconds, let last = lastCountdownUpdateTime else {
            unifiedDisplaySeconds = actual
            lastCountdownUpdateTime = now
            return actual
        }

        // 先按本地时间推进显示（每满1s减1）
        let elapsed = now.timeIntervalSince(last)
        if elapsed >= 1.0 {
            let dec = Int(elapsed) // 向下取整
            display = max(0, display - dec)
            // 将时间推进 dec 秒，保留余数，避免累计误差
            lastCountdownUpdateTime = last.addingTimeInterval(TimeInterval(dec))
        }

        // 与实际对齐策略
        if actual + 2 < display {
            // 服务端进度快于显示超过2秒，直接对齐向下（避免 00:59 -> 00:56 挣扎）
            display = actual
            lastCountdownUpdateTime = now
        } else if actual > display {
            // 服务端落后，不允许上跳：继续用本地 display
            // 不更新 lastCountdownUpdateTime，保持后续按秒递减
        }

        unifiedDisplaySeconds = display
        return display
    }

    private func updateGiftButtonWithActiveData() {
        let title = getActiveGiftTitle()
        updateGiftButtonTitle(title)
    }

    // MARK: - UI更新定时器

    /// 启动UI更新定时器
    private func startUIUpdateTimer() {
        stopUIUpdateTimer() // 先停止之前的定时器

        // 使用0.5秒的更新频率，确保倒计时更加平滑
        uiUpdateTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { [weak self] _ in
            // 每0.5秒更新一次UI，确保倒计时显示平滑
            self?.updateGiftButtonWithActiveData()

            // 如果弹窗存在且是倒计时状态，同步更新弹窗
            self?.updatePopupIfNeeded()
        }
    }

    /// 更新弹窗倒计时（如果弹窗存在且是倒计时状态）
    private func updatePopupIfNeeded() {
        guard let popup = currentGoldPopup else { return }

        let manager = ActivityProgressManager.shared
        guard let currentReward = manager.getCurrentRewardConfig() else { return }

        if manager.hasAvailableRewards() {
            // 同步切换为可领取态，并更新确认逻辑
            popup.configure(mode: .reward,
                            valueText: "\(currentReward.rewardValue)金币",
                            subtitleText: nil)
            popup.onConfirm = { [weak self] in
                self?.handleActiveRewardClaim(configId: currentReward.id)
            }
        } else {
            // 倒计时态：用统一显示值
            let displaySeconds = getUnifiedDisplaySeconds()
            if displaySeconds <= 0 {
                // 本地倒计时到0但不可领取：触发一次快速校准
                reconcileAfterLocalCountdownIfNeeded(source: "popup_tick")
                popup.configure(mode: .countdown,
                                valueText: "00:00",
                                subtitleText: nil)
                popup.onConfirm = { }
            } else {
                popup.configure(mode: .countdown,
                                valueText: formatSeconds(displaySeconds),
                                subtitleText: "活跃度倒计时结束即可获得\(currentReward.rewardValue)金币！")
                // 倒计时态下，确认键不弹提示，减少文字打扰
                popup.onConfirm = { }
            }
        }
    }

    /// 停止UI更新定时器
    private func stopUIUpdateTimer() {
        uiUpdateTimer?.invalidate()
        uiUpdateTimer = nil
    }

    // MARK: - 页面进入流程与页内上报

    /// 本地倒计时走完后（显示到 00:00），若服务端仍未到“待领取”，进行一次快速校准：
    /// 1) 先刷新服务端状态；
    /// 2) 若已变为可领取，立即切弹窗为领取态；
    /// 3) 若还差 <= reconcileToleranceSeconds 秒，则立马上报差额并刷新；
    /// 4) 避免重新开一个新的 3s 倒数。
    private func reconcileAfterLocalCountdownIfNeeded(source: String) {
        if isReconcilingAfterLocalEnd { return }
        let manager = ActivityProgressManager.shared

        // 已可领取则无需校准
        if manager.hasAvailableRewards() { return }

        isReconcilingAfterLocalEnd = true
        print("[TaskCenter][校准] 本地倒计时结束触发校准，来源=\(source)")
        // HUD菊花
        showLoadingIndicator()

        manager.refreshActiveDataWithCompletion { [weak self] data in
            guard let self = self else { return }
            let status = manager.getTreasureBoxStatus()
            let remaining = manager.getCurrentRewardRemainingSeconds() ?? 0
            print("[TaskCenter][校准] 刷新后 status=\(status)，remaining=\(remaining)")

            if status == 1 {
                // 已可领取：同步UI
                self.evaluateAndHandleStatus(using: data, source: "校准-刷新可领取")
                self.isReconcilingAfterLocalEnd = false
                self.hideLoadingIndicator()
                return
            }
            if status == 2 {
                // 今日已完成
                self.evaluateAndHandleStatus(using: data, source: "校准-刷新已完成")
                self.isReconcilingAfterLocalEnd = false
                self.hideLoadingIndicator()
                return
            }

            // 倒计时态：若只差少量秒数，直接补报
            if remaining > 0 && remaining <= self.reconcileToleranceSeconds {
                print("[TaskCenter][校准] 剩余\(remaining)s（<=\(self.reconcileToleranceSeconds)），直接补报")
                self.stopPageReportTimer()
                manager.reportProgress(seconds: remaining) { [weak self] success, newData in
                    guard let self = self else { return }
                    if success {
                        print("[TaskCenter][校准] 补报\(remaining)s成功，刷新状态")
                        manager.refreshActiveDataWithCompletion { [weak self] refreshed in
                            guard let self = self else { return }
                            self.evaluateAndHandleStatus(using: refreshed, source: "校准-补报后")
                            self.isReconcilingAfterLocalEnd = false
                            self.hideLoadingIndicator()
                        }
                    } else {
                        print("[TaskCenter][校准] 补报失败，保持现状")
                        self.isReconcilingAfterLocalEnd = false
                        self.hideLoadingIndicator()
                    }
                }
            } else {
                // 差距较大：不做强行补报，回退到服务端剩余
                if remaining > 0 {
                    print("[TaskCenter][校准] 差距较大(>\(self.reconcileToleranceSeconds)s)，回退至服务端剩余\(remaining)s")
                    self.unifiedDisplaySeconds = remaining
                    self.lastCountdownUpdateTime = Date()
                    self.updateGiftButtonWithActiveData()
                    self.updatePopupIfNeeded()
                }
                self.isReconcilingAfterLocalEnd = false
                self.hideLoadingIndicator()
            }
        }
    }

    /// 进入任务中心后的主流程：刷新 -> 评估状态 -> 补报页面外累计 -> 再评估 -> 决定是否开启3秒上报
    private func startEnterTaskCenterFlow() {
        let manager = ActivityProgressManager.shared
        print("[TaskCenter] 进入页面：开始拉取活跃度/宝箱进度")
        manager.refreshActiveDataWithCompletion { [weak self] data in
            guard let self = self else { return }
            self.evaluateAndHandleStatus(using: data, source: "首次刷新")

            // 若当前为倒计时状态，尝试消耗页面外累计
            if manager.getTreasureBoxStatus() == 0 {
                manager.consumeOutsideAccumulationIfNeeded { [weak self] afterData in
                    guard let self = self else { return }
                    self.evaluateAndHandleStatus(using: afterData, source: "补报后")
                }
            }
        }
    }

    /// 开启页内3秒上报（不在任务中心外页上报）
    private func startPageReportTimer() {
        guard pageReportTimer == nil else { return }
        isPageReporting = true
        print("[TaskCenter] 启动页内3秒上报计时器")
        pageReportTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            ActivityProgressManager.shared.reportProgress(seconds: 3) { [weak self] success, data in
                guard let self = self else { return }
                if success {
                    // 上报成功后，服务端会返回最新数据；依据最新状态决定是否结束上报
                    self.evaluateAndHandleStatus(using: data, source: "3秒上报返回")
                } else {
                    print("[TaskCenter] 3秒上报失败，本轮忽略")
                }
            }
        }
    }

    /// 停止页内3秒上报
    private func stopPageReportTimer() {
        if pageReportTimer != nil { print("[TaskCenter] 停止页内3秒上报计时器") }
        pageReportTimer?.invalidate()
        pageReportTimer = nil
        isPageReporting = false
    }

    /// 统一评估并处理服务器状态（0=倒计时，1=待领取，2=今日已完成）
    private func evaluateAndHandleStatus(using data: ActiveProgressData?, source: String) {
        let manager = ActivityProgressManager.shared
        let status = manager.getTreasureBoxStatus()
        let remaining = manager.getCurrentRewardRemainingSeconds() ?? 0
        print("[TaskCenter][状态] 来源=\(source)，status=\(status)，remaining=\(remaining)")

        switch status {
        case 2:
            // 今日已完成
            stopPageReportTimer()
            manager.clearOutsideAccumulatedSeconds()
            // 结束态重置统一显示秒数
            unifiedDisplaySeconds = nil
            lastCountdownUpdateTime = nil
            updateGiftButtonWithActiveData()
        case 1:
            // 待领取
            stopPageReportTimer()
            manager.clearOutsideAccumulatedSeconds()
            // 可领取态重置统一显示秒数
            unifiedDisplaySeconds = nil
            lastCountdownUpdateTime = nil
            updateGiftButtonWithActiveData()
            // 若弹窗仍在显示倒计时，立即同步到可领取态
            if let _ = currentGoldPopup, let reward = manager.getCurrentRewardConfig() {
                currentGoldPopup?.configure(mode: .reward,
                                            valueText: "\(reward.rewardValue)金币",
                                            subtitleText: nil)
                currentGoldPopup?.onConfirm = { [weak self] in
                    self?.handleActiveRewardClaim(configId: reward.id)
                }
            }
            // 不主动弹窗，保持按钮点击查看
        default:
            // 倒计时状态，确保页内上报在运行
            if pageReportTimer == nil {
                startPageReportTimer()
            }
            updateGiftButtonWithActiveData()
        }
    }

    // MARK: - 应用前后台事件（用于页面外累计）
    @objc private func handleAppWillResignActive() {
        // 应用将进入后台时，如果当前在任务中心，开始页面外累计
        ActivityProgressManager.shared.markLeaveTaskCenterPage()
        // 同时停止页内上报，防止后台重复上报
        stopPageReportTimer()
    }

    @objc private func handleAppDidBecomeActive() {
        // 应用回到前台，若当前页面可见，恢复进入流程（会自动消耗页面外累计并判断是否继续上报）
        ActivityProgressManager.shared.markEnterTaskCenterPage()
        startEnterTaskCenterFlow()
    }

    // MARK: - 活跃度进度更新处理

    @objc private func handleActivityProgressUpdated(_ notification: Notification) {
        // 从通知中获取更新的活跃度数据
        if let data = notification.userInfo?["data"] as? ActiveProgressData {
            activeProgressData = data
            updateGiftButtonWithActiveData()
            print("[TaskCenter] 活跃度数据已更新: 总时长=\(data.totalSeconds)秒")

            // 打印当前状态用于调试
            logCurrentRewardStatus()
        }
    }



    // MARK: - 测试方法（用于调试）

    /// 测试活跃度功能（仅用于调试）
    func testActivityFeatures() {
        print("[TaskCenter] 开始测试活跃度功能...")

        // 1. 测试查询活跃度状态
        print("[TaskCenter] 1. 测试查询活跃度状态")
        ActivityProgressManager.shared.refreshActiveData()

        // 2. 测试手动上报活跃度
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            print("[TaskCenter] 2. 测试手动上报活跃度（1秒）")
            ActivityProgressManager.shared.reportProgress(seconds: 1)
        }

        // 3. 测试宝箱状态检查
        DispatchQueue.main.asyncAfter(deadline: .now() + 4) {
            print("[TaskCenter] 3. 测试宝箱状态检查")
            let manager = ActivityProgressManager.shared
            print("   - 实时总活跃时长: \(manager.getCurrentTotalActiveSeconds())秒")
            print("   - 是否有可领取奖励: \(manager.hasAvailableRewards())")
            print("   - 当前可领取奖励: \(manager.getCurrentAvailableReward())金币")
            if let remaining = manager.getCurrentRewardRemainingSeconds() {
                print("   - 当前宝箱剩余时间: \(remaining)秒")
            }
            if let currentReward = manager.getCurrentRewardConfig() {
                print("   - 当前宝箱配置: ID=\(currentReward.id), 条件值=\(currentReward.conditionValue), 奖励=\(currentReward.rewardValue)")
            }

            print("[TaskCenter] ✅ 活跃度宝箱逻辑修正完成")

            // 4. 测试实时倒计时（每5秒打印一次状态）
            self.testRealTimeCountdown()
        }
    }



    /// 测试实时倒计时功能
    private func testRealTimeCountdown() {
        // 先停止之前的测试定时器
        stopTestTimer()

        var testCount = 0
        testTimer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { [weak self] timer in
            testCount += 1
            let manager = ActivityProgressManager.shared

            // 获取详细的活跃度调试信息
            let debugInfo = manager.getActivityDebugInfo()
            let remaining = manager.getCurrentRewardRemainingSeconds() ?? 0
            let buttonText = self?.getActiveGiftTitle() ?? "未知"

            let startTimeStr = debugInfo.startTime?.timeIntervalSince1970.description ?? "nil"

            print("[倒计时测试 \(testCount)] 基础: \(debugInfo.baseSeconds)秒, 会话: \(debugInfo.sessionSeconds)秒, 总计: \(debugInfo.totalSeconds)秒")
            print("   剩余: \(remaining)秒, 按钮: \(buttonText), 开始时间: \(startTimeStr)")

            // 测试10次后停止
            if testCount >= 10 {
                self?.stopTestTimer()
                print("[倒计时测试] 测试完成")
            }
        }
    }

    /// 停止测试定时器
    private func stopTestTimer() {
        testTimer?.invalidate()
        testTimer = nil
    }

//    private func currentRewardCoins() -> Int {
//        let mgr = GoldCountdownManager.shared
//        return mgr.rewardCoins > 0 ? mgr.rewardCoins : defaultRewardCoins
//    }

    private func formatSeconds(_ seconds: Int) -> String {
        let m = seconds / 60
        let s = seconds % 60
        return String(format: "%02d:%02d", m, s)
    }

    // MARK: - Loading Indicator
    private var loadingIndicator: UIActivityIndicatorView?
    
    private func showLoadingIndicator() {
        guard loadingIndicator == nil else { return }
        
        let indicator = UIActivityIndicatorView(style: .medium)
        indicator.color = .appThemeOrange
        indicator.translatesAutoresizingMaskIntoConstraints = false
        
        view.addSubview(indicator)
        NSLayoutConstraint.activate([
            indicator.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            indicator.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 100)
        ])
        
        indicator.startAnimating()
        loadingIndicator = indicator
    }
    
    private func hideLoadingIndicator() {
        loadingIndicator?.stopAnimating()
        loadingIndicator?.removeFromSuperview()
        loadingIndicator = nil
    }
    
    // MARK: - Refresh Control
    @objc private func handleRefresh(_ refreshControl: UIRefreshControl) {
        // 刷新数据
        fetchGoldCoinData()
        fetchTaskRewardConfig()
        
        // 延迟停止刷新动画，等待API请求完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            refreshControl.endRefreshing()
        }
    }
    
    // MARK: - Public Methods
    /// 手动刷新任务配置
    func refreshTaskConfig() {
        fetchTaskRewardConfig()
    }
    
    // MARK: - Error Handling
    private func showErrorMessage(_ message: String) {
        showHUD(message, duration: 3.0)
    }
    
    // MARK: - HUD / Toast
    private func showHUD(_ message: String, duration: TimeInterval = 2.0) {
        DispatchQueue.main.async {
            let container = UIView()
            container.backgroundColor = UIColor.black.withAlphaComponent(0.8)
            container.layer.cornerRadius = 8
            container.clipsToBounds = true
            container.alpha = 0

            let label = UILabel()
            label.textColor = .white
            label.font = .systemFont(ofSize: 14)
            label.numberOfLines = 0
            label.textAlignment = .center
            label.text = message
            label.translatesAutoresizingMaskIntoConstraints = false
            container.addSubview(label)

            NSLayoutConstraint.activate([
                label.topAnchor.constraint(equalTo: container.topAnchor, constant: 12),
                label.bottomAnchor.constraint(equalTo: container.bottomAnchor, constant: -12),
                label.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 16),
                label.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -16)
            ])

            guard let window = UIApplication.shared.windows.first(where: { $0.isKeyWindow }) else { return }
            window.addSubview(container)
            container.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                container.centerXAnchor.constraint(equalTo: window.centerXAnchor),
                container.centerYAnchor.constraint(equalTo: window.centerYAnchor),
                container.widthAnchor.constraint(lessThanOrEqualTo: window.widthAnchor, multiplier: 0.8)
            ])

            UIView.animate(withDuration: 0.25, animations: { container.alpha = 1 }) { _ in
                DispatchQueue.main.asyncAfter(deadline: .now() + duration) {
                    UIView.animate(withDuration: 0.25, animations: { container.alpha = 0 }) { _ in
                        container.removeFromSuperview()
                    }
                }
            }
        }
    }

    // MARK: - 事件处理

    @objc private func withdrawButtonTapped() {
        print("提现按钮被点击")
        // 跳转到提现页面
        let withdrawalVC = GoldCoinWithdrawalCenterViewController()
        pushViewControllerSafely(withdrawalVC)
    }

    private func publishVideoTaskTapped() {
        print("发布视频任务被点击")
        let vc = VideoRecordViewController()
        pushViewControllerSafely(vc)
    }

    private func checkInTaskTapped() {
        print("签到任务被点击")
        // 新逻辑：点击仅弹窗确认，确认后才执行签到
        presentCheckInPopup()
    }

    private func inviteFriendsTaskTapped() {
        print("邀请好友任务被点击")
        // 实现邀请好友功能
        inviteFromTaskCenter()
    }

    // MARK: - 邀请好友功能实现

    /// 从任务中心发起邀请
    private func inviteFromTaskCenter() {
        print("[TaskCenter] 邀请好友任务——开始获取邀请码")

        // 显示加载菊花，避免用户在1-3秒空窗期无反馈
        showLoadingIndicator()

        // 1) 获取邀请码（模拟网络延迟）
        getInviteCode { [weak self] inviteCode in
            guard let self = self else { return }
            print("[TaskCenter] 已获取到邀请码: \(inviteCode)")

            // 2) 生成海报（将二维码贴到海报右下角）
                let shareText = "马上领取大额优惠，复制口令打开【树小柒】APP \(inviteCode)"
                self.buildInvitePoster(with: shareText) { poster in
                // 3) 组装 SharePayload 并发送通知，走 TabBar 的分享弹窗
                // 隐藏加载菊花
                self.hideLoadingIndicator()
                let payload = SharePayload(
                    title: "树小柒 · 邀请海报",
                    description: shareText,
                    link: nil,
                    thumbnail: nil,
                    image: poster,
                    type: .image,
                    extras: ["shareText": shareText, "command": inviteCode]
                )

                print("[TaskCenter] 发送分享通知（图片+口令） …")
                NotificationCenter.default.post(name: .shareRequested, object: payload)
                print("[TaskCenter] 分享通知已发送")
            }
        }
    }

    /// 获取邀请码
    private func getInviteCode(completion: @escaping (String) -> Void) {
        // 调用真实API获取邀请链接
        APIManager.shared.getInviteLink { [weak self] result in
            guard let self = self else { return }
            
            switch result {
            case .success(let response):
                if let shareLink = response.data?.shareLink, !shareLink.isEmpty {
                    print("[TaskCenter] API获取邀请链接成功: \(shareLink)")
                    completion(shareLink)
                } else {
                    print("[TaskCenter] API返回数据为空，使用默认邀请码")
                    completion("XXX123")
                }
            case .failure(let error):
                print("[TaskCenter] API获取邀请链接失败: \(error.localizedDescription)")
                // API失败时使用默认邀请码
                completion("XXX123")
            }
        }
    }

    /// 构建邀请海报：下载底图 -> 生成二维码 -> 覆盖到右下角
    private func buildInvitePoster(with code: String, completion: @escaping (UIImage?) -> Void) {
        let posterURLString = "https://image.gzyoushu.com/c94cd674d3264df9a5fcd52e6aac1c09.png"
        guard let url = URL(string: posterURLString) else { completion(nil); return }

        // 异步下载海报
        DispatchQueue.global().async {
            let posterImage: UIImage?
            if let data = try? Data(contentsOf: url) {
                posterImage = UIImage(data: data)
            } else {
                posterImage = nil
            }

            // 主线程继续合成
            DispatchQueue.main.async {
                guard let poster = posterImage else { completion(nil); return }

                // 生成二维码图片（按最新要求：270x270）
                guard let qrImage = self.generateQRCode(for: code, size: CGSize(width: 270, height: 270)) else {
                    completion(poster)
                    return
                }

                // 合成：将二维码贴到海报右下角（右边距70pt、下边距240pt）
                let scale = poster.scale
                let canvasSize = poster.size

                UIGraphicsBeginImageContextWithOptions(canvasSize, false, scale)
                defer { UIGraphicsEndImageContext() }

                // 背景海报
                poster.draw(in: CGRect(origin: .zero, size: canvasSize))

                // 计算二维码绘制区域
                let qrSize = CGSize(width: 270, height: 270)
                let originX = canvasSize.width - 70 - qrSize.width
                let originY = canvasSize.height - 240 - qrSize.height
                let qrRect = CGRect(x: originX, y: originY, width: qrSize.width, height: qrSize.height)

                // 绘制二维码（带白底，增强对比）
                let backgroundPath = UIBezierPath(roundedRect: qrRect.insetBy(dx: -4, dy: -4), cornerRadius: 6)
                UIColor.white.setFill()
                backgroundPath.fill()
                qrImage.draw(in: qrRect)

                let composed = UIGraphicsGetImageFromCurrentImageContext()
                completion(composed)
            }
        }
    }

    /// 处理邀请码绑定（供外部调用）
    /// - Parameter invitationCode: 邀请码
    func handleInvitationCodeBinding(invitationCode: String) {
        print("[TaskCenter] 处理外部邀请码绑定: \(invitationCode)")
        
        let alert = UIAlertController(
            title: "绑定邀请码", 
            message: "检测到邀请码：\(invitationCode)\n是否绑定该邀请码获得奖励？", 
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel, handler: nil))
        alert.addAction(UIAlertAction(title: "绑定", style: .default) { _ in
            print("[TaskCenter] 用户确认绑定邀请码")
            self.bindInvitationCode(invitationCode: invitationCode)
        })
        
        present(alert, animated: true)
    }
    
    /// 绑定邀请码到服务器
    /// - Parameter invitationCode: 邀请码
    private func bindInvitationCode(invitationCode: String) {
        print("[TaskCenter] 开始绑定邀请码: \(invitationCode)")
        
        showHUD("正在绑定邀请码...")
        
        APIManager.shared.getShareInfo(shareStr: invitationCode) { [weak self] result in
            DispatchQueue.main.async {
                self?.hideLoadingIndicator()
                
                switch result {
                case .success(let response):
                    // getShareInfo API uses status == 1 for success, not status == 200
                    if response.status == 1 {
                        print("[TaskCenter] 邀请码绑定成功")
                        self?.showHUD("邀请码绑定成功！")
                        
                        // 刷新任务配置和金币数据
                        self?.fetchTaskRewardConfig()
                        self?.fetchGoldCoinData()
                    } else {
                        print("[TaskCenter] 邀请码绑定失败: \(response.displayMessage)")
                        let errorMessage = response.displayMessage.isEmpty ? "绑定失败，请稍后重试" : response.displayMessage
                        self?.showHUD(errorMessage)
                    }
                case .failure(let error):
                    print("[TaskCenter] 邀请码绑定请求失败: \(error.localizedDescription)")
                    self?.showHUD("绑定失败，请检查网络后重试")
                }
            }
        }
    }

    /// 按指定尺寸生成二维码
    private func generateQRCode(for string: String, size: CGSize) -> UIImage? {
        guard let filter = CIFilter(name: "CIQRCodeGenerator") else { return nil }
        filter.setValue(string.data(using: .utf8), forKey: "inputMessage")
        filter.setValue("H", forKey: "inputCorrectionLevel")
        guard let ciImage = filter.outputImage else { return nil }

        // 放大到目标尺寸
        let extent = ciImage.extent.integral
        let scaleX = size.width / extent.width
        let scaleY = size.height / extent.height
        let transformed = ciImage.transformed(by: CGAffineTransform(scaleX: scaleX, y: scaleY))

        let context = CIContext()
        guard let cgImage = context.createCGImage(transformed, from: transformed.extent) else { return nil }
        return UIImage(cgImage: cgImage, scale: UIScreen.main.scale, orientation: .up)
    }

    private func watchVideoTaskTapped() {
        print("观看视频任务被点击")

        // 显示加载状态
        showHUD("正在获取视频...")

        // 使用首页推荐接口获取视频列表
        APIManager.shared.getMainWorksInfo { [weak self] result in
            DispatchQueue.main.async {
                self?.hideLoadingIndicator()

                switch result {
                case .success(let response):
                    if response.isSuccess, let worksGroups = response.data, !worksGroups.isEmpty {
                        // 从所有分组中收集所有视频
                        var allVideos: [VideoItem] = []
                        for group in worksGroups {
                            if let videos = group.list {
                                allVideos.append(contentsOf: videos)
                            }
                        }

                        if !allVideos.isEmpty {
                            // 随机选择一个视频进行播放
                            let randomIndex = Int.random(in: 0..<allVideos.count)
                            let selectedVideo = allVideos[randomIndex]
                            print("随机选择视频: ID=\(selectedVideo.id ?? 0), 标题=\(selectedVideo.worksTitle ?? "无标题"), 总视频数=\(allVideos.count)")

                            // 跳转到视频播放页面，使用正常浏览模式
                            self?.navigateToVideoDisplay(with: selectedVideo, videoList: allVideos, startIndex: randomIndex)
                        } else {
                            print("获取到的视频列表为空")
                            self?.showErrorMessage("暂无可用视频，请稍后重试")
                        }
                    } else {
                        print("获取首页推荐失败: \(response.displayMessage)")
                        self?.showErrorMessage("获取视频失败，请稍后重试")
                    }
                case .failure(let error):
                    print("获取首页推荐请求失败: \(error.localizedDescription)")
                    self?.showErrorMessage("网络连接失败，请检查网络后重试")
                }
            }
        }
    }

    /// 跳转到视频播放页面
    private func navigateToVideoDisplay(with videoItem: VideoItem, videoList: [VideoItem]? = nil, startIndex: Int = 0) {
        let videoDisplayVC: VideoDisplayCenterViewController

        if let list = videoList, !list.isEmpty {
            // 使用正常浏览模式，传入完整视频列表
            videoDisplayVC = VideoDisplayCenterViewController(
                videoList: list,
                startIndex: startIndex,
                hideNavBackButton: false,
                showCustomNavBar: true,
                needsTabBarOffset: false
            )
            print("跳转到视频播放页面（正常浏览模式）: 起始索引=\(startIndex), 总视频数=\(list.count)")
        } else {
            // 降级到单视频模式
            videoDisplayVC = VideoDisplayCenterViewController(
                singleVideoItem: videoItem,
                hideNavBackButton: false,
                showCustomNavBar: true,
                needsTabBarOffset: false
            )
            print("跳转到视频播放页面（单视频模式）: \(videoItem.worksTitle ?? "无标题")")
        }

        // 跳转到视频播放页面
        pushViewControllerSafely(videoDisplayVC)
    }

    /// 去分享任务被点击：弹出分享弹窗（参考 TabBar 的用法）
    private func shareTaskTapped() {
        print("去分享任务被点击 —— 弹出分享弹窗")

        let sheet = ShareSheetView()

        // 可按需定制额外回调或统计，这里直接使用默认行为
        if let window = view.window ?? UIApplication.shared.windows.first(where: { $0.isKeyWindow }) {
            print("显示分享弹窗（任务中心）")
            sheet.show(in: window)
        } else {
            print("❌ 无法找到合适的窗口显示分享弹窗（任务中心）")
        }
    }

    // 自定义签到弹窗（先实现背景与关闭按钮，内容后续补充）
    private func presentCheckInPopup() {
        // 若已存在先移除
        currentCheckInPopup?.dismiss(animated: false)

        let popup = DailyAttendancePopupView()
        // 动态获取最后一个签到配置的金币数量
        let lastDayReward = checkInDays.last?.reward ?? 18888
        // 动态文案 + 已连签天数
        popup.configure(
            title: "连续签到7天得\(lastDayReward)金币",
            subtitle: "已连签\(consecutiveCheckInDays)/7天，断签将无法得到最终大奖"
        )
        popup.onDismiss = { [weak self] in
            self?.currentCheckInPopup = nil
        }
        popup.onConfirm = { [weak self, weak popup] in
            self?.performCheckIn()
            popup?.dismiss(animated: true)
        }
        // 计算今天应该签到的天数（已签到天数+1）
        let signedDaysCount = checkInDays.filter { $0.isCheckedIn }.count
        let todayIndex = signedDaysCount // 今天要签到的索引（0-based）

        print("[TaskCenter] 弹出签到弹窗 - 已签到天数: \(signedDaysCount), 今日索引: \(todayIndex)")
        print("[TaskCenter] 当前签到数据: \(checkInDays.map { "第\($0.day): \($0.reward)金币, 已签到: \($0.isCheckedIn)" })")

        // 展示今日可领金币数
        var todayReward = 0
        if todayIndex < checkInDays.count {
            todayReward = checkInDays[todayIndex].reward
        }
        print("[TaskCenter] 今日可领取奖励: \(todayReward)金币")
        popup.setCoinAmount(todayReward)

        // 设置签到进度（当前应签到的索引 + 每日奖励文案）
        let amounts: [Int] = (0..<7).map { i in
            return (i < checkInDays.count) ? checkInDays[i].reward : 0
        }
        print("[TaskCenter] 传递给弹窗的奖励数组: \(amounts)")
        popup.setProgress(currentIndex: todayIndex, amounts: amounts)
        popup.show(in: self.view)
        currentCheckInPopup = popup
    }

    private func performCheckIn() {
        // 调用签到API
        APIManager.shared.userSign { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if response.status == 200 {
                        // 签到成功，刷新任务配置以获取最新状态
                        self?.fetchTaskRewardConfig()
                        self?.fetchGoldCoinData()
                        self?.showHUD("签到成功！")
                    } else {
                        self?.showHUD(response.errMsg ?? "签到失败")
                    }
                case .failure(let error):
                    self?.showHUD("签到失败：\(error.localizedDescription)")
                }
            }
        }
    }

    // MARK: - 内存管理

    deinit {
        // 清理定时器
        stopUIUpdateTimer()
        stopTestTimer()
        // 移除通知监听
        NotificationCenter.default.removeObserver(self)
        print("[TaskCenter] 任务中心控制器已释放")
    }
}

// MARK: - 数据模型

struct CheckInDay {
    let day: String
    let reward: Int
    var isCheckedIn: Bool
}
